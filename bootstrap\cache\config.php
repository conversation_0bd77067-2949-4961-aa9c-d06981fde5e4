<?php return array (
  2 => 'broadcasting',
  4 => 'concurrency',
  5 => 'cors',
  8 => 'hashing',
  14 => 'view',
  'app' => 
  array (
    'name' => 'Laravel',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'Africa/Nairobi',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:X6YQCrZVC7J89Nd28DEp/gCzmXgNFAfczEDp/h6ftvk=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
      6 => 'Illuminate\\Cookie\\CookieServiceProvider',
      7 => 'Illuminate\\Database\\DatabaseServiceProvider',
      8 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      9 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      10 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      11 => 'Illuminate\\Hashing\\HashServiceProvider',
      12 => 'Illuminate\\Mail\\MailServiceProvider',
      13 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      14 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      15 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      16 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      17 => 'Illuminate\\Queue\\QueueServiceProvider',
      18 => 'Illuminate\\Redis\\RedisServiceProvider',
      19 => 'Illuminate\\Session\\SessionServiceProvider',
      20 => 'Illuminate\\Translation\\TranslationServiceProvider',
      21 => 'Illuminate\\Validation\\ValidationServiceProvider',
      22 => 'Illuminate\\View\\ViewServiceProvider',
      23 => 'App\\Providers\\AppServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Concurrency' => 'Illuminate\\Support\\Facades\\Concurrency',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Context' => 'Illuminate\\Support\\Facades\\Context',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schedule' => 'Illuminate\\Support\\Facades\\Schedule',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Uri' => 'Illuminate\\Support\\Uri',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'cache' => 
  array (
    'default' => 'database',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'cache',
        'lock_connection' => NULL,
        'lock_table' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\framework/cache/data',
        'lock_path' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'marbar_',
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'marbar_dash',
        'prefix' => '',
        'foreign_key_constraints' => true,
        'busy_timeout' => NULL,
        'journal_mode' => NULL,
        'synchronous' => NULL,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'marbar_dash',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'marbar_dash',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'marbar_dash',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'marbar_dash',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 
    array (
      'table' => 'migrations',
      'update_date_on_publish' => true,
    ),
    'redis' => 
    array (
      'client' => 'predis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'laravel_database_',
        'persistent' => false,
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\app/private',
        'serve' => true,
        'throw' => false,
        'report' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\app/public',
        'url' => 'http://localhost/storage',
        'visibility' => 'public',
        'throw' => false,
        'report' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
    ),
    'links' => 
    array (
      'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\public\\storage' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\app/public',
    ),
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'handler_with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'formatter' => NULL,
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'log',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'scheme' => NULL,
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '2525',
        'username' => NULL,
        'password' => NULL,
        'timeout' => NULL,
        'local_domain' => 'localhost',
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
        'retry_after' => 60,
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
        'retry_after' => 60,
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Laravel',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'permission' => 
  array (
    'models' => 
    array (
      'permission' => 'App\\Models\\Permission',
      'role' => 'App\\Models\\Role',
    ),
    'table_names' => 
    array (
      'roles' => 'roles',
      'permissions' => 'permissions',
      'model_has_permissions' => 'model_has_permissions',
      'model_has_roles' => 'model_has_roles',
      'role_has_permissions' => 'role_has_permissions',
    ),
    'column_names' => 
    array (
      'role_pivot_key' => NULL,
      'permission_pivot_key' => NULL,
      'model_morph_key' => 'model_id',
      'team_foreign_key' => 'team_id',
    ),
    'register_permission_check_method' => true,
    'register_octane_reset_listener' => false,
    'events_enabled' => false,
    'teams' => false,
    'team_resolver' => 'Spatie\\Permission\\DefaultTeamResolver',
    'use_passport_client_credentials' => false,
    'display_permission_in_exception' => false,
    'display_role_in_exception' => false,
    'enable_wildcard_permission' => false,
    'cache' => 
    array (
      'expiration_time' => 
      \DateInterval::__set_state(array(
         'from_string' => true,
         'date_string' => '24 hours',
      )),
      'key' => 'spatie.permission.cache',
      'store' => 'default',
    ),
  ),
  'permissions' => 
  array (
    'permissions' => 
    array (
      'user_management' => 
      array (
        'module' => 'users',
        'permissions' => 
        array (
          'users.view' => 
          array (
            'name' => 'View Users',
            'description' => 'View user list and user details',
          ),
          'users.create' => 
          array (
            'name' => 'Create Users',
            'description' => 'Create new user accounts',
          ),
          'users.edit' => 
          array (
            'name' => 'Edit Users',
            'description' => 'Edit existing user information',
          ),
          'users.delete' => 
          array (
            'name' => 'Delete Users',
            'description' => 'Delete user accounts',
          ),
          'users.assign_roles' => 
          array (
            'name' => 'Assign User Roles',
            'description' => 'Assign and manage user roles',
          ),
          'users.manage_permissions' => 
          array (
            'name' => 'Manage User Permissions',
            'description' => 'Grant or revoke specific permissions to users',
          ),
          'users.export' => 
          array (
            'name' => 'Export User Data',
            'description' => 'Export user data and reports',
          ),
        ),
      ),
      'patient_management' => 
      array (
        'module' => 'patients',
        'permissions' => 
        array (
          'patients.view' => 
          array (
            'name' => 'View Patients',
            'description' => 'View patient list and patient details',
          ),
          'patients.view_assigned' => 
          array (
            'name' => 'View Assigned Patients',
            'description' => 'View only patients assigned to the current therapist',
          ),
          'patients.create' => 
          array (
            'name' => 'Register Patients',
            'description' => 'Register new patients in the system',
          ),
          'patients.edit' => 
          array (
            'name' => 'Edit Patient Information',
            'description' => 'Edit existing patient information',
          ),
          'patients.delete' => 
          array (
            'name' => 'Delete Patient Records',
            'description' => 'Delete patient records (restricted)',
          ),
          'patients.medical_history' => 
          array (
            'name' => 'View Medical History',
            'description' => 'Access patient medical history',
          ),
          'patients.assign_therapist' => 
          array (
            'name' => 'Assign Therapists',
            'description' => 'Assign therapists to patients',
          ),
          'patients.export' => 
          array (
            'name' => 'Export Patient Data',
            'description' => 'Export patient data and reports',
          ),
          'patients.analytics' => 
          array (
            'name' => 'Patient Analytics',
            'description' => 'View patient analytics and demographics',
          ),
        ),
      ),
      'therapist_management' => 
      array (
        'module' => 'therapists',
        'permissions' => 
        array (
          'therapists.view' => 
          array (
            'name' => 'View Therapists',
            'description' => 'View therapist directory and profiles',
          ),
          'therapists.create' => 
          array (
            'name' => 'Add Therapists',
            'description' => 'Add new therapists to the system',
          ),
          'therapists.edit' => 
          array (
            'name' => 'Edit Therapist Profiles',
            'description' => 'Edit therapist information and profiles',
          ),
          'therapists.delete' => 
          array (
            'name' => 'Remove Therapists',
            'description' => 'Remove therapists from the system',
          ),
          'therapists.assign_patients' => 
          array (
            'name' => 'Assign Patients',
            'description' => 'Assign patients to therapists',
          ),
          'therapists.view_constellation' => 
          array (
            'name' => 'View Patient Constellation',
            'description' => 'View therapist patient assignments',
          ),
          'therapists.manage_schedule' => 
          array (
            'name' => 'Manage Schedules',
            'description' => 'Manage therapist schedules and availability',
          ),
          'therapists.analytics' => 
          array (
            'name' => 'Therapist Analytics',
            'description' => 'View therapist performance analytics',
          ),
        ),
      ),
      'clinic_management' => 
      array (
        'module' => 'clinics',
        'permissions' => 
        array (
          'clinics.view' => 
          array (
            'name' => 'View Clinics',
            'description' => 'View clinic list and clinic details',
          ),
          'clinics.create' => 
          array (
            'name' => 'Create Clinics',
            'description' => 'Register new clinics in the system',
          ),
          'clinics.edit' => 
          array (
            'name' => 'Edit Clinic Information',
            'description' => 'Edit existing clinic information',
          ),
          'clinics.delete' => 
          array (
            'name' => 'Delete Clinics',
            'description' => 'Delete clinic records',
          ),
          'clinics.manage_staff' => 
          array (
            'name' => 'Manage Clinic Staff',
            'description' => 'Manage clinic staff assignments',
          ),
          'clinics.manage_services' => 
          array (
            'name' => 'Manage Services',
            'description' => 'Manage clinic services and offerings',
          ),
          'clinics.operational_history' => 
          array (
            'name' => 'View Operational History',
            'description' => 'View clinic operational history',
          ),
          'clinics.analytics' => 
          array (
            'name' => 'Clinic Analytics',
            'description' => 'View clinic performance analytics',
          ),
        ),
      ),
      'researcher_management' => 
      array (
        'module' => 'researchers',
        'permissions' => 
        array (
          'researchers.view' => 
          array (
            'name' => 'View Researchers',
            'description' => 'View researcher directory and profiles',
          ),
          'researchers.create' => 
          array (
            'name' => 'Add Researchers',
            'description' => 'Add new researchers to the system',
          ),
          'researchers.edit' => 
          array (
            'name' => 'Edit Researcher Profiles',
            'description' => 'Edit researcher information and credentials',
          ),
          'researchers.delete' => 
          array (
            'name' => 'Remove Researchers',
            'description' => 'Remove researchers from the system',
          ),
          'researchers.manage_projects' => 
          array (
            'name' => 'Manage Research Projects',
            'description' => 'Create and manage research projects',
          ),
          'researchers.manage_publications' => 
          array (
            'name' => 'Manage Publications',
            'description' => 'Manage research publications',
          ),
          'researchers.analytics' => 
          array (
            'name' => 'Research Analytics',
            'description' => 'View research analytics and metrics',
          ),
        ),
      ),
      'diagnoses_management' => 
      array (
        'module' => 'diagnoses',
        'permissions' => 
        array (
          'diagnoses.view' => 
          array (
            'name' => 'View Diagnoses',
            'description' => 'View diagnoses list and diagnosis details',
          ),
          'diagnoses.create' => 
          array (
            'name' => 'Add Diagnoses',
            'description' => 'Add new diagnoses to the system',
          ),
          'diagnoses.edit' => 
          array (
            'name' => 'Edit Diagnoses',
            'description' => 'Edit existing diagnosis information',
          ),
          'diagnoses.delete' => 
          array (
            'name' => 'Delete Diagnoses',
            'description' => 'Delete diagnosis records',
          ),
          'diagnoses.categories' => 
          array (
            'name' => 'Manage Diagnosis Categories',
            'description' => 'Manage diagnosis categories and classifications',
          ),
          'diagnoses.analytics' => 
          array (
            'name' => 'Diagnosis Analytics',
            'description' => 'View diagnosis analytics and reports',
          ),
        ),
      ),
      'role_management' => 
      array (
        'module' => 'roles',
        'permissions' => 
        array (
          'roles.view' => 
          array (
            'name' => 'View Roles',
            'description' => 'View system roles and permissions',
          ),
          'roles.create' => 
          array (
            'name' => 'Create Roles',
            'description' => 'Create new system roles',
          ),
          'roles.edit' => 
          array (
            'name' => 'Edit Roles',
            'description' => 'Edit existing roles and permissions',
          ),
          'roles.delete' => 
          array (
            'name' => 'Delete Roles',
            'description' => 'Delete custom roles (not system roles)',
          ),
          'permissions.view' => 
          array (
            'name' => 'View Permissions',
            'description' => 'View system permissions',
          ),
          'permissions.create' => 
          array (
            'name' => 'Create Permissions',
            'description' => 'Create custom permissions',
          ),
          'permissions.edit' => 
          array (
            'name' => 'Edit Permissions',
            'description' => 'Edit permission details',
          ),
          'permissions.delete' => 
          array (
            'name' => 'Delete Permissions',
            'description' => 'Delete custom permissions',
          ),
        ),
      ),
      'system_administration' => 
      array (
        'module' => 'system',
        'permissions' => 
        array (
          'system.dashboard' => 
          array (
            'name' => 'System Dashboard',
            'description' => 'Access system administration dashboard',
          ),
          'system.settings' => 
          array (
            'name' => 'System Settings',
            'description' => 'Manage system configuration and settings',
          ),
          'system.maintenance' => 
          array (
            'name' => 'System Maintenance',
            'description' => 'Perform system maintenance tasks',
          ),
          'system.backup' => 
          array (
            'name' => 'System Backup',
            'description' => 'Create and manage system backups',
          ),
          'system.logs' => 
          array (
            'name' => 'View System Logs',
            'description' => 'View and manage system logs',
          ),
          'system.audit' => 
          array (
            'name' => 'Audit Trail',
            'description' => 'View system audit trail and user activities',
          ),
        ),
      ),
      'analytics' => 
      array (
        'module' => 'analytics',
        'permissions' => 
        array (
          'analytics.dashboard' => 
          array (
            'name' => 'Analytics Dashboard',
            'description' => 'Access analytics dashboard',
          ),
          'analytics.reports' => 
          array (
            'name' => 'Generate Reports',
            'description' => 'Generate and view system reports',
          ),
          'analytics.export' => 
          array (
            'name' => 'Export Analytics',
            'description' => 'Export analytics data and reports',
          ),
          'analytics.advanced' => 
          array (
            'name' => 'Advanced Analytics',
            'description' => 'Access advanced analytics features',
          ),
        ),
      ),
      'communication' => 
      array (
        'module' => 'notifications',
        'permissions' => 
        array (
          'notifications.view' => 
          array (
            'name' => 'View Notifications',
            'description' => 'View system notifications',
          ),
          'notifications.send' => 
          array (
            'name' => 'Send Notifications',
            'description' => 'Send notifications to users',
          ),
          'notifications.manage' => 
          array (
            'name' => 'Manage Notifications',
            'description' => 'Manage notification settings and templates',
          ),
          'communication.contact' => 
          array (
            'name' => 'Contact Management',
            'description' => 'Manage contact forms and communications',
          ),
        ),
      ),
    ),
    'default_role_permissions' => 
    array (
      'superadmin' => '*',
      'admin' => 
      array (
        0 => 'users.view',
        1 => 'users.create',
        2 => 'users.edit',
        3 => 'users.assign_roles',
        4 => 'users.export',
        5 => 'patients.view',
        6 => 'patients.create',
        7 => 'patients.edit',
        8 => 'patients.medical_history',
        9 => 'patients.assign_therapist',
        10 => 'patients.export',
        11 => 'patients.analytics',
        12 => 'therapists.view',
        13 => 'therapists.create',
        14 => 'therapists.edit',
        15 => 'therapists.assign_patients',
        16 => 'therapists.view_constellation',
        17 => 'therapists.analytics',
        18 => 'clinics.view',
        19 => 'clinics.create',
        20 => 'clinics.edit',
        21 => 'clinics.manage_staff',
        22 => 'clinics.manage_services',
        23 => 'clinics.analytics',
        24 => 'researchers.view',
        25 => 'researchers.create',
        26 => 'researchers.edit',
        27 => 'researchers.analytics',
        28 => 'diagnoses.view',
        29 => 'diagnoses.create',
        30 => 'diagnoses.edit',
        31 => 'diagnoses.categories',
        32 => 'diagnoses.analytics',
        33 => 'roles.view',
        34 => 'permissions.view',
        35 => 'analytics.dashboard',
        36 => 'analytics.reports',
        37 => 'analytics.export',
        38 => 'notifications.view',
        39 => 'notifications.send',
        40 => 'notifications.manage',
        41 => 'system.dashboard',
        42 => 'system.logs',
      ),
      'therapist' => 
      array (
        0 => 'patients.view',
        1 => 'patients.medical_history',
        2 => 'patients.analytics',
        3 => 'therapists.view',
        4 => 'therapists.view_constellation',
        5 => 'clinics.view',
        6 => 'analytics.dashboard',
        7 => 'notifications.view',
        8 => 'diagnoses.view',
        9 => 'diagnoses.analytics',
      ),
      'therapist_assigned_only' => 
      array (
        0 => 'patients.view_assigned',
        1 => 'patients.medical_history',
        2 => 'patients.analytics',
        3 => 'therapists.view',
        4 => 'therapists.view_constellation',
        5 => 'clinics.view',
        6 => 'analytics.dashboard',
        7 => 'notifications.view',
        8 => 'diagnoses.view',
        9 => 'diagnoses.analytics',
      ),
      'researcher' => 
      array (
        0 => 'patients.view',
        1 => 'patients.analytics',
        2 => 'therapists.view',
        3 => 'clinics.view',
        4 => 'researchers.view',
        5 => 'researchers.manage_projects',
        6 => 'researchers.manage_publications',
        7 => 'researchers.analytics',
        8 => 'analytics.*',
        9 => 'diagnoses.view',
        10 => 'diagnoses.analytics',
      ),
      'patient' => 
      array (
        0 => 'notifications.view',
      ),
      'user' => 
      array (
        0 => 'notifications.view',
      ),
    ),
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'batching' => 
    array (
      'database' => 'mysql',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
  ),
  'session' => 
  array (
    'driver' => 'database',
    'lifetime' => 120,
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'laravel_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'cluster' => NULL,
          'host' => 'api-mt1.pusher.com',
          'port' => 443,
          'scheme' => 'https',
          'encrypted' => true,
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => '12',
      'verify' => true,
      'limit' => NULL,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
      'verify' => true,
    ),
    'rehash_on_login' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\resources\\views',
    ),
    'compiled' => 'C:\\xampp\\htdocs\\Marbar-Africa\\Marbar-AFRICA\\storage\\framework\\views',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
