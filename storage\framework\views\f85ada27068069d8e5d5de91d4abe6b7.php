<aside class="app-sidebar shadow">
    <!--begin::Sidebar Brand-->
    <div class="sidebar-brand">
        <!--begin::Brand Link-->
        <a href="<?php echo e(route('dashboard')); ?>" class="brand-link">
            <!--begin::Brand Image-->
            <img src="../../dist/assets/img/AdminLTELogo.png" alt="Health Analytics Logo"
                class="brand-image opacity-75 shadow" />
            <!--end::Brand Image-->
            <!--begin::Brand Text-->
            <span class="brand-text fw-light">MarbarBar Africa</span>
            <!--end::Brand Text-->
        </a>
        <!--end::Brand Link-->
    </div>
    <!--end::Sidebar Brand-->
    <!--begin::Sidebar Wrapper-->
    <div class="sidebar-wrapper">
        <nav class="mt-2">
            <!--begin::Sidebar Menu-->
            <ul class="nav sidebar-menu flex-column" data-lte-toggle="treeview" role="menu" data-accordion="false">
                <!-- Dashboard -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('analytics.dashboard')): ?>
                <li class="nav-item menu-open">
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-link active">
                        <i class="nav-icon bi bi-speedometer2"></i>
                        <p>Analytics Dashboard</p>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Global Search -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['patients.view', 'therapists.view', 'clinics.view', 'researchers.view', 'users.view'])): ?>
                <li class="nav-item">
                    <a href="<?php echo e(route('search.index')); ?>" class="nav-link">
                        <i class="nav-icon bi bi-search"></i>
                        <p>Global Search</p>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Patient Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['patients.view', 'patients.view_assigned', 'patients.create', 'patients.edit', 'patients.medical_history'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('patient-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('patient-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-person-lines-fill"></i>
                        <p>
                            Patient Management
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('patients.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('patient-management.registry.index')); ?>" class="nav-link <?php echo e(request()->routeIs('patient-management.registry.index', 'patient-management.registry.view', 'patient-management.registry.edit', 'patient-management.registry.medical-history') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Patients</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('patients.view_assigned')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('patient-management.registry.my-assigned')); ?>" class="nav-link <?php echo e(request()->routeIs('patient-management.registry.my-assigned') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>My Assigned Patients</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('patients.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('patient-management.registry.create')); ?>" class="nav-link <?php echo e(request()->routeIs('patient-management.registry.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Add Patients</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('patients.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('patient-management.registry.assigned')); ?>" class="nav-link <?php echo e(request()->routeIs('patient-management.registry.assigned') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Assigned Patients</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Therapist Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['therapists.view', 'therapists.create', 'therapists.edit', 'therapists.view_constellation'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('therapist-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('therapist-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-clipboard2-pulse"></i>
                        <p>
                            Therapist Management
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>

                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('therapists.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('therapist-management.directory.index')); ?>" class="nav-link <?php echo e(request()->routeIs('therapist-management.directory.index', 'therapist-management.directory.view', 'therapist-management.directory.constellation', 'therapist-management.directory.edit') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Therapists</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('therapists.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('therapist-management.directory.create')); ?>" class="nav-link <?php echo e(request()->routeIs('therapist-management.directory.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Add Therapist</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                <!-- Clinic Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['clinics.view', 'clinics.create', 'clinics.edit', 'clinics.manage_staff'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('clinic-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('clinic-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-building"></i>
                        <p>
                            Clinic Management
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('clinics.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('clinic-management.registry.index')); ?>" class="nav-link <?php echo e(request()->routeIs('clinic-management.registry.index', 'clinic-management.registry.view', 'clinic-management.registry.edit', 'clinic-management.registry.operational-history') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Clinics</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('clinics.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('clinic-management.registry.create')); ?>" class="nav-link <?php echo e(request()->routeIs('clinic-management.registry.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Add Clinic</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>



                <!-- Researcher Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['researchers.view', 'researchers.create', 'researchers.edit', 'researchers.analytics'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('researcher-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('researcher-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-person-heart"></i>
                        <p>
                            Researcher Management
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('researchers.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('researcher-management.registry.index')); ?>" class="nav-link <?php echo e(request()->routeIs('researcher-management.registry.*') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Researchers</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('researchers.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('researcher-management.registry.create')); ?>" class="nav-link <?php echo e(request()->routeIs('researcher-management.registry.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Add Researcher</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('researchers.analytics')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('researcher-management.analytics.index')); ?>" class="nav-link <?php echo e(request()->routeIs('researcher-management.analytics.*') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Analytics</p>
                            </a>
                        </li>
                        <?php endif; ?>





                    </ul>
                </li>
                <?php endif; ?>

                <!-- Diagnoses Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['diagnoses.view', 'diagnoses.create', 'diagnoses.edit', 'diagnoses.categories', 'diagnoses.analytics'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('diagnoses-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('diagnoses-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-clipboard2-pulse"></i>
                        <p>
                            Diagnoses Management
                            <span class="nav-badge badge text-bg-info me-3">Dx</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('diagnoses-management.index')); ?>" class="nav-link <?php echo e(request()->routeIs('diagnoses-management.index', 'diagnoses-management.show') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Diagnoses</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('diagnoses-management.create')); ?>" class="nav-link <?php echo e(request()->routeIs('diagnoses-management.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Add Diagnosis</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.categories')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('diagnoses-management.categories.index')); ?>" class="nav-link <?php echo e(request()->routeIs('diagnoses-management.categories.*') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Diagnosis Categories</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.analytics')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('diagnoses-management.analytics.index')); ?>" class="nav-link <?php echo e(request()->routeIs('diagnoses-management.analytics.*') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Diagnosis Analytics</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- User Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['users.view', 'users.create', 'users.edit', 'users.assign_roles'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('user-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('user-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-people-fill"></i>
                        <p>
                            User Management
                            <span class="nav-badge badge text-bg-primary me-3">Users</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user-management.index')); ?>" class="nav-link <?php echo e(request()->routeIs('user-management.index', 'user-management.show', 'user-management.edit') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Users</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user-management.create')); ?>" class="nav-link <?php echo e(request()->routeIs('user-management.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Add User</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>


                <!-- System Logs -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('system.logs')): ?>
                <li class="nav-item">
                    <a href="<?php echo e(route('system-logs.index')); ?>" class="nav-link <?php echo e(request()->routeIs('system-logs.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-journal-text"></i>
                        <p>
                            System Logs
                            <span class="nav-badge badge text-bg-info me-3">Logs</span>
                        </p>
                    </a>
                </li>
                <?php endif; ?>




                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['roles.view', 'permissions.view', 'users.assign_roles'])): ?>
                <li class="nav-header">ROLE & PERMISSIONS</li>
                <?php endif; ?>

                <!-- Role Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['roles.view', 'roles.create', 'roles.edit'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('role-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('role-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-shield-lock"></i>
                        <p>
                            Role Management
                            <span class="nav-badge badge text-bg-primary me-3">Roles</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('role-management.index')); ?>" class="nav-link <?php echo e(request()->routeIs('role-management.index', 'role-management.show', 'role-management.edit') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Roles</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('role-management.create')); ?>" class="nav-link <?php echo e(request()->routeIs('role-management.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Create Role</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('role-management.demo')); ?>" class="nav-link <?php echo e(request()->routeIs('role-management.demo') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Role Demo</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- Permission Management -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['permissions.view', 'permissions.create', 'permissions.edit'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('permission-management.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('permission-management.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-key"></i>
                        <p>
                            Permission Management
                            <span class="nav-badge badge text-bg-success me-3">Perms</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('permission-management.index')); ?>" class="nav-link <?php echo e(request()->routeIs('permission-management.index', 'permission-management.show', 'permission-management.edit') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>All Permissions</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('permission-management.create')); ?>" class="nav-link <?php echo e(request()->routeIs('permission-management.create') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Create Permission</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('permission-management.groups')); ?>" class="nav-link <?php echo e(request()->routeIs('permission-management.groups') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Permission Groups</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- User Role Assignment -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['users.assign_roles', 'users.manage_permissions'])): ?>
                <li class="nav-item <?php echo e(request()->routeIs('user-role-assignment.*') ? 'menu-open' : ''); ?>">
                    <a href="#" class="nav-link <?php echo e(request()->routeIs('user-role-assignment.*') ? 'active' : ''); ?>">
                        <i class="nav-icon bi bi-person-gear"></i>
                        <p>
                            User Role Assignment
                            <span class="nav-badge badge text-bg-warning me-3">Assign</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.assign_roles')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user-role-assignment.index')); ?>" class="nav-link <?php echo e(request()->routeIs('user-role-assignment.index') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Assign Roles</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.assign_roles')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user-role-assignment.bulk')); ?>" class="nav-link <?php echo e(request()->routeIs('user-role-assignment.bulk') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Bulk Assignment</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.manage_permissions')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user-role-assignment.matrix')); ?>" class="nav-link <?php echo e(request()->routeIs('user-role-assignment.matrix') ? 'active' : ''); ?>">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Permission Matrix</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>















































            </ul>
            <!--end::Sidebar Menu-->
        </nav>
    </div>
    <!--end::Sidebar Wrapper-->
</aside>
<?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/includes/sidebar.blade.php ENDPATH**/ ?>