   <nav class="app-header navbar navbar-expand">
            <!--begin::Container-->
            <div class="container-fluid">
                <!--begin::Start Navbar Links-->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button">
                            <i class="bi bi-list"></i>
                        </a>
                    </li>
                    <li class="nav-item d-none d-md-block"><a href="<?php echo e(route('dashboard')); ?>" class="nav-link">Home</a></li>
                    <li class="nav-item d-none d-md-block"><a href="<?php echo e(route('contact.index')); ?>" class="nav-link">Contact</a></li>
                </ul>
                <!--end::Start Navbar Links-->
                <!--begin::End Navbar Links-->
                <ul class="navbar-nav ms-auto">
                    <!--begin::Navbar Search-->
                    <li class="nav-item dropdown">
                        <div class="nav-search-container position-relative">
                            <form action="<?php echo e(route('search.index')); ?>" method="GET" class="d-flex">
                                <div class="input-group">
                                    <input type="text"
                                           class="form-control form-control-sm"
                                           name="q"
                                           id="navSearchInput"
                                           placeholder="Search analytics, patients, reports..."
                                           autocomplete="off"
                                           style="width: 250px;">
                                    <button class="btn btn-outline-secondary btn-sm" type="submit">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </form>

                            <!-- Search Suggestions Dropdown -->
                            <div id="searchSuggestions" class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto; display: none;">
                                <div class="dropdown-header">
                                    <i class="bi bi-search me-1"></i>
                                    Search Suggestions
                                </div>
                                <div id="suggestionsList">
                                    <!-- AJAX suggestions will be loaded here -->
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center" href="#" id="viewAllResults">
                                    <i class="bi bi-arrow-right me-1"></i>
                                    View all results
                                </a>
                            </div>
                        </div>
                    </li>
                    <!--end::Navbar Search-->

                    <!--begin::Role & Permission Quick Access-->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['roles.view', 'permissions.view', 'users.assign_roles'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link" data-bs-toggle="dropdown" href="#" title="Role & Permission Management">
                            <i class="bi bi-shield-lock"></i>
                            <span class="navbar-badge badge text-bg-primary">R&P</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                            <span class="dropdown-item dropdown-header">
                                <i class="bi bi-shield-lock me-1"></i>
                                Role & Permission Management
                            </span>
                            <div class="dropdown-divider"></div>

                            <!-- Role Management -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['roles.view', 'roles.create'])): ?>
                            <h6 class="dropdown-header">
                                <i class="bi bi-shield-lock me-1"></i>
                                Roles
                            </h6>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles.view')): ?>
                            <a href="<?php echo e(route('role-management.index')); ?>" class="dropdown-item">
                                <i class="bi bi-list me-2"></i> All Roles
                                <span class="float-end text-secondary fs-7"><?php echo e(\App\Models\Role::count()); ?></span>
                            </a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles.create')): ?>
                            <a href="<?php echo e(route('role-management.create')); ?>" class="dropdown-item">
                                <i class="bi bi-plus-circle me-2"></i> Create Role
                            </a>
                            <?php endif; ?>
                            <div class="dropdown-divider"></div>
                            <?php endif; ?>

                            <!-- Permission Management -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['permissions.view', 'permissions.create'])): ?>
                            <h6 class="dropdown-header">
                                <i class="bi bi-key me-1"></i>
                                Permissions
                            </h6>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions.view')): ?>
                            <a href="<?php echo e(route('permission-management.index')); ?>" class="dropdown-item">
                                <i class="bi bi-list me-2"></i> All Permissions
                                <span class="float-end text-secondary fs-7"><?php echo e(\App\Models\Permission::count()); ?></span>
                            </a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permissions.create')): ?>
                            <a href="<?php echo e(route('permission-management.create')); ?>" class="dropdown-item">
                                <i class="bi bi-plus-circle me-2"></i> Create Permission
                            </a>
                            <?php endif; ?>
                            <div class="dropdown-divider"></div>
                            <?php endif; ?>

                            <!-- Quick Actions -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['users.assign_roles', 'users.manage_permissions'])): ?>
                            <h6 class="dropdown-header">
                                <i class="bi bi-lightning me-1"></i>
                                Quick Actions
                            </h6>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.assign_roles')): ?>
                            <a href="<?php echo e(route('user-role-assignment.index')); ?>" class="dropdown-item">
                                <i class="bi bi-person-gear me-2"></i> Assign User Roles
                            </a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.manage_permissions')): ?>
                            <a href="<?php echo e(route('user-role-assignment.matrix')); ?>" class="dropdown-item">
                                <i class="bi bi-grid-3x3-gap me-2"></i> Permission Matrix
                            </a>
                            <?php endif; ?>
                            <div class="dropdown-divider"></div>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('roles.view')): ?>
                            <a href="<?php echo e(route('role-management.demo')); ?>" class="dropdown-item dropdown-footer">
                                <i class="bi bi-play-circle me-1"></i>
                                View Demo
                            </a>
                            <?php endif; ?>
                        </div>
                    </li>
                    <?php endif; ?>
                    <!--end::Role & Permission Quick Access-->

                    <!--begin::Messages Dropdown Menu-->
                    <li class="nav-item dropdown">
                        <a class="nav-link" data-bs-toggle="dropdown" href="#">
                            <i class="bi bi-chat-text"></i>
                            <span class="navbar-badge badge text-bg-danger">3</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                            <a href="<?php echo e(route('contact.index')); ?>" class="dropdown-item">
                                <!--begin::Message-->
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <img src="../../dist/assets/img/user1-128x128.jpg" alt="User Avatar"
                                            class="img-size-50 rounded-circle me-3" />
                                    </div>
                                    <div class="flex-grow-1">
                                        <h3 class="dropdown-item-title">
                                            Brad Diesel
                                            <span class="float-end fs-7 text-danger"><i
                                                    class="bi bi-star-fill"></i></span>
                                        </h3>
                                        <p class="fs-7">Call me whenever you can...</p>
                                        <p class="fs-7 text-secondary">
                                            <i class="bi bi-clock-fill me-1"></i> 4 Hours Ago
                                        </p>
                                    </div>
                                </div>
                                <!--end::Message-->
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('contact.index')); ?>" class="dropdown-item">
                                <!--begin::Message-->
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <img src="../../dist/assets/img/user8-128x128.jpg" alt="User Avatar"
                                            class="img-size-50 rounded-circle me-3" />
                                    </div>
                                    <div class="flex-grow-1">
                                        <h3 class="dropdown-item-title">
                                            John Pierce
                                            <span class="float-end fs-7 text-secondary">
                                                <i class="bi bi-star-fill"></i>
                                            </span>
                                        </h3>
                                        <p class="fs-7">I got your message bro</p>
                                        <p class="fs-7 text-secondary">
                                            <i class="bi bi-clock-fill me-1"></i> 4 Hours Ago
                                        </p>
                                    </div>
                                </div>
                                <!--end::Message-->
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('contact.index')); ?>" class="dropdown-item">
                                <!--begin::Message-->
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <img src="../../dist/assets/img/user3-128x128.jpg" alt="User Avatar"
                                            class="img-size-50 rounded-circle me-3" />
                                    </div>
                                    <div class="flex-grow-1">
                                        <h3 class="dropdown-item-title">
                                            Nora Silvester
                                            <span class="float-end fs-7 text-warning">
                                                <i class="bi bi-star-fill"></i>
                                            </span>
                                        </h3>
                                        <p class="fs-7">The subject goes here</p>
                                        <p class="fs-7 text-secondary">
                                            <i class="bi bi-clock-fill me-1"></i> 4 Hours Ago
                                        </p>
                                    </div>
                                </div>
                                <!--end::Message-->
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('contact.index')); ?>" class="dropdown-item dropdown-footer">See All Messages</a>
                        </div>
                    </li>
                    <!--end::Messages Dropdown Menu-->
                    <!--begin::Notifications Dropdown Menu-->
                    <li class="nav-item dropdown">
                        <a class="nav-link" data-bs-toggle="dropdown" href="#">
                            <i class="bi bi-bell-fill"></i>
                            <span class="navbar-badge badge text-bg-warning">15</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                            <span class="dropdown-item dropdown-header">15 Notifications</span>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('notifications.index')); ?>" class="dropdown-item">
                                <i class="bi bi-envelope me-2"></i> 4 new messages
                                <span class="float-end text-secondary fs-7">3 mins</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('notifications.index')); ?>" class="dropdown-item">
                                <i class="bi bi-people-fill me-2"></i> 8 friend requests
                                <span class="float-end text-secondary fs-7">12 hours</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('notifications.index')); ?>" class="dropdown-item">
                                <i class="bi bi-file-earmark-fill me-2"></i> 3 new reports
                                <span class="float-end text-secondary fs-7">2 days</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo e(route('notifications.index')); ?>" class="dropdown-item dropdown-footer"> See All Notifications </a>
                        </div>
                    </li>
                    <!--end::Notifications Dropdown Menu-->
                    <!--begin::Fullscreen Toggle-->
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-lte-toggle="fullscreen">
                            <i data-lte-icon="maximize" class="bi bi-arrows-fullscreen"></i>
                            <i data-lte-icon="minimize" class="bi bi-fullscreen-exit" style="display: none"></i>
                        </a>
                    </li>
                    <!--end::Fullscreen Toggle-->
                    <!--begin::Lock Screen Toggle-->
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="lock-screen-btn"
                           title="Lock Screen Manually"
                           data-bs-toggle="tooltip"
                           data-bs-placement="bottom"
                           onclick="return manualLockScreen(event);">
                            <i class="bi bi-lock-fill"></i>
                        </a>
                    </li>
                    <!--end::Lock Screen Toggle-->
                    <!--begin::Theme Toggle-->
                    <li class="nav-item">
                        <a class="nav-link theme-toggle" href="#" id="theme-toggle" title="Switch to Dark Theme">
                            <i id="theme-light-icon" class="bi bi-sun-fill" style="display: none;"></i>
                            <i id="theme-dark-icon" class="bi bi-moon-fill"></i>
                        </a>
                    </li>
                    <!--end::Theme Toggle-->
                    <!--begin::User Menu Dropdown-->
                    <li class="nav-item dropdown user-menu">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img src="<?php echo e(Auth::check() && Auth::user()->profile_image ? asset('storage/' . Auth::user()->profile_image) : '../../dist/assets/img/user2-160x160.jpg'); ?>"
                                 class="user-image rounded-circle shadow" alt="User Image" />
                            <span class="d-none d-md-inline"><?php echo e(Auth::check() ? Auth::user()->name : 'Guest'); ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                            <!--begin::User Image-->
                            <li class="user-header text-bg-primary">
                                <img src="<?php echo e(Auth::check() && Auth::user()->profile_image ? asset('storage/' . Auth::user()->profile_image) : '../../dist/assets/img/user2-160x160.jpg'); ?>"
                                     class="rounded-circle shadow" alt="User Image" />
                                <p>
                                    <?php echo e(Auth::check() ? Auth::user()->name : 'Guest'); ?> - <?php echo e(Auth::check() ? ucfirst(Auth::user()->role ?? 'User') : 'Guest'); ?>

                                    <small>Member since <?php echo e(Auth::check() ? Auth::user()->created_at->format('M Y') : 'N/A'); ?></small>
                                </p>
                            </li>
                            <!--end::User Image-->
                            <!--begin::Menu Body-->
                            <li class="user-body">
                                <!--begin::Row-->
                                <div class="row">
                                    <div class="col-4 text-center"><a href="<?php echo e(route('profile.edit')); ?>">Profile</a></div>
                                    <div class="col-4 text-center"><a href="#">Settings</a></div>
                                </div>
                                <!--end::Row-->
                            </li>
                            <!--end::Menu Body-->
                            <!--begin::Menu Footer-->
                            <li class="user-footer">
                                <a href="<?php echo e(route('session.destroy')); ?>" class="btn btn-default btn-flat float-end">Sign
                                    out</a>
                            </li>
                            <!--end::Menu Footer-->
                        </ul>
                    </li>
                    <!--end::User Menu Dropdown-->
                </ul>
                <!--end::End Navbar Links-->
            </div>
            <!--end::Container-->
        </nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('navSearchInput');
    const suggestionsDropdown = document.getElementById('searchSuggestions');
    const suggestionsList = document.getElementById('suggestionsList');
    const viewAllResults = document.getElementById('viewAllResults');
    let searchTimeout;

    if (searchInput) {
        // Handle input events
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                hideSuggestions();
                return;
            }

            searchTimeout = setTimeout(() => {
                fetchSuggestions(query);
            }, 300);
        });

        // Handle focus events
        searchInput.addEventListener('focus', function() {
            const query = this.value.trim();
            if (query.length >= 2) {
                fetchSuggestions(query);
            }
        });

        // Handle click outside to hide suggestions
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !suggestionsDropdown.contains(e.target)) {
                hideSuggestions();
            }
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideSuggestions();
            }
        });
    }

    function fetchSuggestions(query) {
        fetch(`<?php echo e(route('search.ajax')); ?>?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySuggestions(data.suggestions, query);
            })
            .catch(error => {
                console.error('Search error:', error);
                hideSuggestions();
            });
    }

    function displaySuggestions(suggestions, query) {
        suggestionsList.innerHTML = '';

        if (suggestions.length === 0) {
            suggestionsList.innerHTML = '<div class="dropdown-item-text text-muted">No suggestions found</div>';
        } else {
            suggestions.forEach(suggestion => {
                const item = document.createElement('a');
                item.className = 'dropdown-item d-flex align-items-center';
                item.href = suggestion.url;
                item.innerHTML = `
                    <i class="bi ${suggestion.icon} me-2 text-primary"></i>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${highlightQuery(suggestion.text, query)}</div>
                        <small class="text-muted">${suggestion.subtitle}</small>
                    </div>
                    <small class="text-muted">${suggestion.category}</small>
                `;
                suggestionsList.appendChild(item);
            });
        }

        // Update "View all results" link
        viewAllResults.href = `<?php echo e(route('search.index')); ?>?q=${encodeURIComponent(query)}`;

        showSuggestions();
    }

    function highlightQuery(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    function showSuggestions() {
        suggestionsDropdown.style.display = 'block';
        suggestionsDropdown.classList.add('show');
    }

    function hideSuggestions() {
        suggestionsDropdown.style.display = 'none';
        suggestionsDropdown.classList.remove('show');
    }

    // Initialize tooltips for lock screen button
    const lockScreenBtn = document.getElementById('lock-screen-btn');
    if (lockScreenBtn && typeof bootstrap !== 'undefined') {
        new bootstrap.Tooltip(lockScreenBtn);
    }

    // Add event listener as backup for manual lock button
    if (lockScreenBtn) {
        lockScreenBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof manualLockScreen === 'function') {
                manualLockScreen(e);
            } else {
                // Direct fallback if function not available
                window.location.href = '/lock-screen';
            }
            return false;
        });
    }
});

// Manual Lock Screen Handler Function
function manualLockScreen(event) {
    event.preventDefault();

    // Show confirmation for manual lock
    try {
        if (typeof toastr !== 'undefined' && toastr.info) {
            toastr.info('Locking screen...', 'Security');
        } else {
            console.log('Locking screen...');
        }
    } catch (error) {
        console.log('Locking screen...');
    }

    // Check if the lock screen manager is available
    if (window.lockScreenManager && typeof window.lockScreenManager.manualLock === 'function') {
        // Use the existing lock screen manager
        window.lockScreenManager.manualLock();
    } else {
        // Fallback: Direct redirect to lock screen (simple approach)
        console.log('Using direct redirect to lock screen');
        window.location.href = '/lock-screen';
    }

    return false;
}

// Direct Lock Screen Function (fallback)
function lockScreenDirectly() {
    console.log('Direct lock screen function called');

    // Show loading state on button
    const lockBtn = document.getElementById('lock-screen-btn');
    if (!lockBtn) {
        console.error('Lock screen button not found');
        alert('Lock screen button not found');
        return;
    }

    console.log('Lock screen button found, updating UI');
    const originalContent = lockBtn.innerHTML;
    lockBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    lockBtn.style.pointerEvents = 'none';

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (!csrfToken) {
        console.error('CSRF token not found');
        try {
            if (typeof toastr !== 'undefined' && toastr.error) {
                toastr.error('Security token not found. Please refresh the page.');
            } else {
                alert('Security token not found. Please refresh the page.');
            }
        } catch (error) {
            alert('Security token not found. Please refresh the page.');
        }
        lockBtn.innerHTML = originalContent;
        lockBtn.style.pointerEvents = 'auto';
        return;
    }

    console.log('Making AJAX request to lock screen');

    // Make AJAX request to lock the screen
    fetch('/lock-screen/lock', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
            'Accept': 'application/json'
        },
        body: JSON.stringify({})
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Show success message before redirect
            try {
                if (typeof toastr !== 'undefined' && toastr.success) {
                    toastr.success('Screen locked successfully', 'Security');
                } else {
                    console.log('Screen locked successfully');
                }
            } catch (error) {
                console.log('Screen locked successfully');
            }
            // Small delay to show the message
            setTimeout(() => {
                window.location.href = '/lock-screen';
            }, 500);
        } else {
            // Show error message
            const message = data.message || 'Failed to lock screen';
            try {
                if (typeof toastr !== 'undefined' && toastr.error) {
                    toastr.error(message, 'Error');
                } else {
                    alert(message);
                }
            } catch (error) {
                alert(message);
            }
            // Restore button
            lockBtn.innerHTML = originalContent;
            lockBtn.style.pointerEvents = 'auto';
        }
    })
    .catch(error => {
        console.error('Lock screen error:', error);
        const message = 'An error occurred while locking the screen. Please try again.';
        try {
            if (typeof toastr !== 'undefined' && toastr.error) {
                toastr.error(message, 'Error');
            } else {
                alert(message);
            }
        } catch (toastrError) {
            alert(message);
        }
        // Restore button
        lockBtn.innerHTML = originalContent;
        lockBtn.style.pointerEvents = 'auto';
    });
}
</script>

<script>
// Fullscreen Navigation Interceptor for Marbar-Africa
(function() {
    'use strict';

    const FULLSCREEN_KEY = 'marbar_fullscreen_mode';
    let fullscreenActive = false;
    let contentContainer = null;

    // Check if currently in fullscreen
    function isInFullscreen() {
        return !!(document.fullscreenElement || document.webkitFullscreenElement ||
                 document.mozFullScreenElement || document.msFullscreenElement);
    }

    // Enter fullscreen
    function enterFullscreen() {
        const elem = document.documentElement;
        if (elem.requestFullscreen) {
            return elem.requestFullscreen();
        } else if (elem.webkitRequestFullscreen) {
            return elem.webkitRequestFullscreen();
        } else if (elem.mozRequestFullScreen) {
            return elem.mozRequestFullScreen();
        } else if (elem.msRequestFullscreen) {
            return elem.msRequestFullscreen();
        }
        return Promise.reject('Fullscreen not supported');
    }

    // Exit fullscreen
    function exitFullscreen() {
        if (document.exitFullscreen) {
            return document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            return document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            return document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            return document.msExitFullscreen();
        }
        return Promise.reject('Exit fullscreen not supported');
    }

    // Update fullscreen icons
    function updateIcons(inFullscreen) {
        const maxIcon = document.querySelector('[data-lte-icon="maximize"]');
        const minIcon = document.querySelector('[data-lte-icon="minimize"]');

        if (maxIcon && minIcon) {
            if (inFullscreen) {
                maxIcon.style.display = 'none';
                minIcon.style.display = 'block';
            } else {
                maxIcon.style.display = 'block';
                minIcon.style.display = 'none';
            }
        }
    }

    // Load content via AJAX instead of navigation
    function loadContent(url) {
        if (!contentContainer) {
            contentContainer = document.querySelector('.app-main') || document.querySelector('main') || document.body;
        }

        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Extract main content from response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newContent = doc.querySelector('.app-main') || doc.querySelector('main');

            if (newContent && contentContainer) {
                contentContainer.innerHTML = newContent.innerHTML;

                // Update page title
                const newTitle = doc.querySelector('title');
                if (newTitle) {
                    document.title = newTitle.textContent;
                }

                // Update URL without navigation
                history.pushState({}, '', url);

                // Re-initialize any scripts in the new content
                const scripts = contentContainer.querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    script.parentNode.replaceChild(newScript, script);
                });
            }
        })
        .catch(error => {
            // Fallback to normal navigation if AJAX fails
            window.location.href = url;
        });
    }

    // Intercept navigation when in fullscreen
    function interceptNavigation() {
        document.addEventListener('click', function(e) {
            if (!fullscreenActive) return;

            const link = e.target.closest('a[href]');
            if (link && link.href &&
                !link.href.includes('javascript:') &&
                !link.href.includes('#') &&
                !link.href.includes('mailto:') &&
                !link.href.includes('tel:') &&
                !link.target &&
                link.href.startsWith(window.location.origin)) {

                e.preventDefault();
                loadContent(link.href);
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', function(e) {
            if (fullscreenActive) {
                loadContent(window.location.href);
            }
        });
    }

    // Setup fullscreen toggle
    function setupToggle() {
        // Disable AdminLTE fullscreen
        if (window.FullScreen) {
            window.FullScreen = function() {};
        }

        const toggle = document.querySelector('[data-lte-toggle="fullscreen"]');
        if (toggle) {
            // Remove existing listeners
            const newToggle = toggle.cloneNode(true);
            toggle.parentNode.replaceChild(newToggle, toggle);

            newToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (isInFullscreen()) {
                    exitFullscreen().then(() => {
                        fullscreenActive = false;
                        localStorage.setItem(FULLSCREEN_KEY, 'false');
                        updateIcons(false);
                    });
                } else {
                    enterFullscreen().then(() => {
                        fullscreenActive = true;
                        localStorage.setItem(FULLSCREEN_KEY, 'true');
                        updateIcons(true);
                    });
                }
            });
        }
    }

    // Handle fullscreen changes
    function handleFullscreenChange() {
        const inFullscreen = isInFullscreen();
        fullscreenActive = inFullscreen;
        localStorage.setItem(FULLSCREEN_KEY, inFullscreen ? 'true' : 'false');
        updateIcons(inFullscreen);
    }

    // Initialize
    function init() {
        setupToggle();
        interceptNavigation();

        // Listen for fullscreen changes
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);

        // Restore fullscreen state
        const wasFullscreen = localStorage.getItem(FULLSCREEN_KEY) === 'true';
        if (wasFullscreen) {
            setTimeout(() => {
                enterFullscreen().then(() => {
                    fullscreenActive = true;
                    updateIcons(true);
                }).catch(() => {
                    localStorage.setItem(FULLSCREEN_KEY, 'false');
                });
            }, 100);
        }
    }

    // Start when ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
</script>

<style>
/* Lock Screen Button Styles */
#lock-screen-btn {
    transition: all 0.2s ease-in-out;
}

#lock-screen-btn:hover {
    color: #dc3545 !important;
    transform: scale(1.1);
}

#lock-screen-btn i {
    transition: all 0.2s ease-in-out;
}

.nav-search-container {
    margin-right: 10px;
}

.nav-search-container .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1050;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.nav-search-container .dropdown-item:hover {
    background-color: #f8f9fa;
}

.nav-search-container mark {
    background-color: #fff3cd;
    padding: 0;
}

@media (max-width: 768px) {
    .nav-search-container input {
        width: 200px !important;
    }

    .nav-search-container .dropdown-menu {
        width: 300px !important;
    }
}

/* Enhanced Fullscreen Styles */
.fullscreen-active {
    /* Add any specific styles for when fullscreen is active */
}

.fullscreen-toggle-icon {
    transition: opacity 0.2s ease-in-out;
}

/* Ensure fullscreen content is properly displayed */
html:fullscreen,
html:-webkit-full-screen,
html:-moz-full-screen {
    background: #fff;
}

html:fullscreen body,
html:-webkit-full-screen body,
html:-moz-full-screen body {
    background: #fff;
    margin: 0;
    padding: 0;
}

/* Lock Screen Button Styles */
#lock-screen-btn {
    transition: all 0.3s ease;
    position: relative;
}

#lock-screen-btn:hover {
    color: #dc3545 !important;
    transform: scale(1.1);
}

#lock-screen-btn:active {
    transform: scale(0.95);
}

#lock-screen-btn i {
    transition: all 0.3s ease;
}

#lock-screen-btn:hover i {
    animation: lockPulse 0.6s ease-in-out;
}

@keyframes lockPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}
</style>
<?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/includes/nav.blade.php ENDPATH**/ ?>