<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to patient_management table (only if they don't exist)
        $this->addIndexIfNotExists('patient_management', ['name'], 'idx_patient_name');
        $this->addIndexIfNotExists('patient_management', ['email'], 'idx_patient_email');
        $this->addIndexIfNotExists('patient_management', ['phone_number'], 'idx_patient_phone');
        $this->addIndexIfNotExists('patient_management', ['study_id'], 'idx_patient_study_id');
        $this->addIndexIfNotExists('patient_management', ['diagnosis'], 'idx_patient_diagnosis');
        $this->addIndexIfNotExists('patient_management', ['status'], 'idx_patient_status');
        $this->addIndexIfNotExists('patient_management', ['risk_level'], 'idx_patient_risk_level');
        $this->addIndexIfNotExists('patient_management', ['assigned_therapist_id'], 'idx_patient_therapist');
        $this->addIndexIfNotExists('patient_management', ['clinic'], 'idx_patient_clinic');
        $this->addIndexIfNotExists('patient_management', ['hospital_id'], 'idx_patient_hospital');
        $this->addIndexIfNotExists('patient_management', ['created_at'], 'idx_patient_created');
        $this->addIndexIfNotExists('patient_management', ['updated_at'], 'idx_patient_updated');
        $this->addIndexIfNotExists('patient_management', ['next_appointment'], 'idx_patient_next_appointment');
        $this->addIndexIfNotExists('patient_management', ['last_session_date'], 'idx_patient_last_session');

        // Add indexes to therapist_management table
        $this->addIndexIfNotExists('therapist_management', ['name'], 'idx_therapist_name');
        $this->addIndexIfNotExists('therapist_management', ['email'], 'idx_therapist_email');
        $this->addIndexIfNotExists('therapist_management', ['phone'], 'idx_therapist_phone');
        $this->addIndexIfNotExists('therapist_management', ['status'], 'idx_therapist_status');
        $this->addIndexIfNotExists('therapist_management', ['specialization'], 'idx_therapist_specialization');
        $this->addIndexIfNotExists('therapist_management', ['primary_clinic'], 'idx_therapist_clinic');
        $this->addIndexIfNotExists('therapist_management', ['license_number'], 'idx_therapist_license');
        $this->addIndexIfNotExists('therapist_management', ['created_at'], 'idx_therapist_created');

        // Add indexes to clinic_management table
        $this->addIndexIfNotExists('clinic_management', ['name'], 'idx_clinic_name');
        $this->addIndexIfNotExists('clinic_management', ['code'], 'idx_clinic_code');
        $this->addIndexIfNotExists('clinic_management', ['status'], 'idx_clinic_status');
        $this->addIndexIfNotExists('clinic_management', ['type'], 'idx_clinic_type');
        $this->addIndexIfNotExists('clinic_management', ['location'], 'idx_clinic_location');
        $this->addIndexIfNotExists('clinic_management', ['site'], 'idx_clinic_site');
        $this->addIndexIfNotExists('clinic_management', ['email'], 'idx_clinic_email');
        $this->addIndexIfNotExists('clinic_management', ['phone_number'], 'idx_clinic_phone');

        // Add indexes to users table
        $this->addIndexIfNotExists('users', ['role'], 'idx_user_role');
        $this->addIndexIfNotExists('users', ['clinic_id'], 'idx_user_clinic');
        $this->addIndexIfNotExists('users', ['phone_number'], 'idx_user_phone');
        $this->addIndexIfNotExists('users', ['created_at'], 'idx_user_created');

        // Add indexes to researcher_management table
        $this->addIndexIfNotExists('researcher_management', ['name'], 'idx_researcher_name');
        $this->addIndexIfNotExists('researcher_management', ['email'], 'idx_researcher_email');
        $this->addIndexIfNotExists('researcher_management', ['phone'], 'idx_researcher_phone');
        $this->addIndexIfNotExists('researcher_management', ['status'], 'idx_researcher_status');
        $this->addIndexIfNotExists('researcher_management', ['institution'], 'idx_researcher_institution');
        $this->addIndexIfNotExists('researcher_management', ['specialization'], 'idx_researcher_specialization');
        $this->addIndexIfNotExists('researcher_management', ['research_id'], 'idx_researcher_research_id');
        $this->addIndexIfNotExists('researcher_management', ['ethics_compliance'], 'idx_researcher_ethics');
        $this->addIndexIfNotExists('researcher_management', ['last_activity'], 'idx_researcher_activity');
        $this->addIndexIfNotExists('researcher_management', ['h_index'], 'idx_researcher_h_index');
        $this->addIndexIfNotExists('researcher_management', ['publications_count'], 'idx_researcher_publications');
        $this->addIndexIfNotExists('researcher_management', ['created_at'], 'idx_researcher_created');

        // Add indexes to system_logs table
        $this->addIndexIfNotExists('system_logs', ['user_id'], 'idx_log_user');
        $this->addIndexIfNotExists('system_logs', ['action'], 'idx_log_action');
        $this->addIndexIfNotExists('system_logs', ['level'], 'idx_log_level');
        $this->addIndexIfNotExists('system_logs', ['category'], 'idx_log_category');
        $this->addIndexIfNotExists('system_logs', ['created_at'], 'idx_log_created');
    }

    /**
     * Helper method to add index only if it doesn't exist
     */
    private function addIndexIfNotExists($table, $columns, $indexName)
    {
        try {
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexesNames = array_keys($sm->listTableIndexes($table));

            if (!in_array($indexName, $indexesNames)) {
                Schema::table($table, function (Blueprint $tableBlueprint) use ($columns, $indexName) {
                    $tableBlueprint->index($columns, $indexName);
                });
            }
        } catch (\Exception $e) {
            // Skip if table doesn't exist or column doesn't exist
            Log::info("Skipping index {$indexName} on table {$table}: " . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes if they exist
        $indexes = [
            'patient_management' => [
                'idx_patient_name', 'idx_patient_email', 'idx_patient_phone', 'idx_patient_study_id',
                'idx_patient_diagnosis', 'idx_patient_status', 'idx_patient_risk_level', 'idx_patient_therapist',
                'idx_patient_clinic', 'idx_patient_hospital', 'idx_patient_created', 'idx_patient_updated',
                'idx_patient_next_appointment', 'idx_patient_last_session'
            ],
            'therapist_management' => [
                'idx_therapist_name', 'idx_therapist_email', 'idx_therapist_phone', 'idx_therapist_status',
                'idx_therapist_specialization', 'idx_therapist_clinic', 'idx_therapist_license', 'idx_therapist_created'
            ],
            'clinic_management' => [
                'idx_clinic_name', 'idx_clinic_code', 'idx_clinic_status', 'idx_clinic_type',
                'idx_clinic_location', 'idx_clinic_site', 'idx_clinic_email', 'idx_clinic_phone'
            ],
            'users' => [
                'idx_user_role', 'idx_user_clinic', 'idx_user_phone', 'idx_user_created'
            ],
            'researcher_management' => [
                'idx_researcher_name', 'idx_researcher_email', 'idx_researcher_phone', 'idx_researcher_status',
                'idx_researcher_institution', 'idx_researcher_specialization', 'idx_researcher_research_id',
                'idx_researcher_ethics', 'idx_researcher_activity', 'idx_researcher_h_index',
                'idx_researcher_publications', 'idx_researcher_created'
            ],
            'system_logs' => [
                'idx_log_user', 'idx_log_action', 'idx_log_level', 'idx_log_category', 'idx_log_created'
            ]
        ];

        foreach ($indexes as $table => $tableIndexes) {
            $this->dropIndexesIfExist($table, $tableIndexes);
        }
    }

    /**
     * Helper method to drop indexes only if they exist
     */
    private function dropIndexesIfExist($table, $indexes)
    {
        try {
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $existingIndexes = array_keys($sm->listTableIndexes($table));

            Schema::table($table, function (Blueprint $tableBlueprint) use ($indexes, $existingIndexes) {
                foreach ($indexes as $indexName) {
                    if (in_array($indexName, $existingIndexes)) {
                        $tableBlueprint->dropIndex($indexName);
                    }
                }
            });
        } catch (\Exception $e) {
            // Skip if table doesn't exist
            Log::info("Skipping dropping indexes on table {$table}: " . $e->getMessage());
        }
    }
};
