@extends('admin.main')

@section('title', 'Patient Details - ' . $patient['name'])

@section('content')
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">
                        <i class="bi bi-person-circle me-2 text-primary"></i>
                        Patient Profile
                    </h3>
                    <p class="text-muted mb-0">Detailed view of patient information and treatment progress</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.dashboard') }}">Patient Management</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('patient-management.registry.index') }}">Registry</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $patient['name'] }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Success Alert-->
            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong>Success!</strong> {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!--begin::Patient Profile Card-->
            <div class="row">
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-body text-center">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                {{ substr($patient->name, 0, 1) }}{{ substr(explode(' ', $patient->name)[1] ?? '', 0, 1) }}
                            </div>
                            <h4 class="mb-1">{{ $patient->name }}</h4>
                            <p class="text-muted mb-2">{{ $patient->email ?? 'No email provided' }}</p>
                            <p class="text-muted mb-3">{{ $patient->phone_number ?? 'No phone provided' }}</p>

                            @php
                                $statusClass = match(strtolower($patient->status)) {
                                    'active' => 'bg-success',
                                    'inactive' => 'bg-secondary',
                                    default => 'bg-danger'
                                };
                                $riskClass = match(strtolower($patient->risk_level)) {
                                    'high' => 'bg-danger',
                                    'medium' => 'bg-warning',
                                    default => 'bg-success'
                                };
                            @endphp

                            <span class="badge {{ $statusClass }} mb-2">{{ ucfirst($patient->status) }}</span><br>
                            <span class="badge {{ $riskClass }} mb-3">{{ $patient->risk_level }} Risk</span>

                            @if($patient['total_risk_score'] > 2)
                            <div class="alert alert-danger mt-3">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>Red Flag Alert!</strong><br>
                                Risk Score: {{ $patient['total_risk_score'] }}/5
                            </div>
                            @endif

                            <div class="d-flex flex-wrap gap-2 justify-content-center mt-3">
                                <a href="{{ route('patient-management.registry.edit', $patient['id']) }}" class="btn btn-success btn-sm">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a href="{{ route('patient-management.registry.medical-history', $patient['id']) }}" class="btn btn-primary btn-sm">
                                    <i class="bi bi-file-medical"></i> History
                                </a>
                                @if($patient['risk_level'] === 'High' || $patient['total_risk_score'] > 2)
                                <button class="btn btn-danger btn-sm" onclick="triggerAlert({{ $patient['id'] }})">
                                    <i class="bi bi-flag"></i> Alert
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!--begin::Personal Information-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-lines-fill me-2"></i>Personal Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-5"><strong>Date of Birth:</strong></div>
                                <div class="col-7">{{ $patient->formatted_dob }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Age:</strong></div>
                                <div class="col-7">{{ $patient->age ?? 'Not calculated' }} years</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Gender:</strong></div>
                                <div class="col-7">{{ $patient->gender }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Address:</strong></div>
                                <div class="col-7">{{ $patient->address ?? 'Not provided' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Next of Kin:</strong></div>
                                <div class="col-7">{{ $patient->next_of_keen ?? 'Not provided' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Hospital:</strong></div>
                                <div class="col-7">
                                    @if($patient->hospital)
                                        {{ $patient->hospital->name }}
                                        @if($patient->hospital->location)
                                            - {{ $patient->hospital->location }}
                                        @endif
                                        @if($patient->hospital->type)
                                            ({{ $patient->hospital->type }})
                                        @endif
                                    @else
                                        Not assigned
                                    @endif
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-5"><strong>Study Information:</strong></div>
                                <div class="col-7">
                                    <strong>Site:</strong> {{ $patient->site }}<br>
                                    <strong>Clinic:</strong> {{ $patient->clinic }}<br>
                                    <strong>Study ID:</strong> {{ $patient->study_id }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--begin::Treatment Information-->
                <div class="col-lg-8">
                    <!--begin::Treatment Overview-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-heart-pulse me-2"></i>Treatment Overview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Diagnosis:</strong></div>
                                        <div class="col-6">
                                            <span class="badge bg-primary">{{ $patient->diagnosis }}</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Assigned Therapist:</strong></div>
                                        <div class="col-6">{{ $patient->assigned_therapist }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Registration Date:</strong></div>
                                        <div class="col-6">{{ $patient->formatted_registration_date }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Status:</strong></div>
                                        <div class="col-6">
                                            <span class="badge {{ $statusClass }}">{{ ucfirst($patient->status) }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Treatment Progress:</strong></div>
                                        <div class="col-6">
                                            <div class="progress">
                                                <div class="progress-bar bg-success" style="width: {{ $patient->treatment_progress }}%">
                                                    {{ $patient->treatment_progress }}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Risk Level:</strong></div>
                                        <div class="col-6">
                                            <span class="badge {{ $riskClass }}">{{ $patient->risk_level }}</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Insurance:</strong></div>
                                        <div class="col-6">{{ $patient->insurance }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Satisfaction Score:</strong></div>
                                        <div class="col-6">
                                            <div class="text-warning">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= floor($patient->satisfaction_score))
                                                        <i class="bi bi-star-fill"></i>
                                                    @elseif($i <= $patient->satisfaction_score)
                                                        <i class="bi bi-star-half"></i>
                                                    @else
                                                        <i class="bi bi-star"></i>
                                                    @endif
                                                @endfor
                                                <small class="text-muted ms-1">({{ $patient->satisfaction_score }})</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Assessment Scores-->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-clipboard-data me-2"></i>CORE-10 Assessment</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div class="display-6 fw-bold {{ $patient->core10_score > 15 ? 'text-danger' : ($patient->core10_score > 10 ? 'text-warning' : 'text-success') }}">
                                        {{ $patient->core10_score }}/40
                                    </div>
                                    <p class="text-muted mb-0">
                                        @if($patient->core10_score > 15)
                                            High Distress
                                        @elseif($patient->core10_score > 10)
                                            Moderate Distress
                                        @else
                                            Not Assessed
                                        @endif
                                    </p>
                                    @if($patient->core10_score > 15)
                                    <div class="alert alert-danger mt-2 mb-0">
                                        <i class="bi bi-exclamation-triangle-fill"></i> Requires attention
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-people me-2"></i>WAI Assessment</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div class="display-6 fw-bold {{ $patient->wai_score < 50 ? 'text-danger' : ($patient->wai_score < 70 ? 'text-warning' : 'text-success') }}">
                                        {{ $patient->wai_score }}/84
                                    </div>
                                    <p class="text-muted mb-0">
                                        @if($patient->wai_score >= 70)
                                            Strong Alliance
                                        @elseif($patient->wai_score >= 50)
                                            Moderate Alliance
                                        @else
                                            Not Assessed
                                        @endif
                                    </p>
                                    @if($patient->wai_score > 0 && $patient->wai_score < 50)
                                    <div class="alert alert-warning mt-2 mb-0">
                                        <i class="bi bi-exclamation-triangle"></i> Needs improvement
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Statistics Overview-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>Treatment Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="h4 text-primary">{{ $patientStats['total_sessions'] }}</div>
                                    <small class="text-muted">Total Sessions</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h4 text-success">{{ $patientStats['completed_assessments'] }}</div>
                                    <small class="text-muted">Assessments</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h4 text-warning">{{ $patientStats['missed_appointments'] }}</div>
                                    <small class="text-muted">Missed Appointments</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="h4 text-info">{{ $patientStats['treatment_duration_months'] }}m</div>
                                    <small class="text-muted">Treatment Duration</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--begin::Recent Activity-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @foreach($recentActivity as $activity)
                                <div class="timeline-item mb-3">
                                    <div class="row">
                                        <div class="col-auto">
                                            <div class="timeline-marker bg-{{ $activity['type'] === 'session' ? 'primary' : ($activity['type'] === 'assessment' ? 'success' : 'info') }} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                @if($activity['type'] === 'session')
                                                    <i class="bi bi-chat-dots"></i>
                                                @elseif($activity['type'] === 'assessment')
                                                    <i class="bi bi-clipboard-check"></i>
                                                @elseif($activity['type'] === 'appointment')
                                                    <i class="bi bi-calendar-check"></i>
                                                @else
                                                    <i class="bi bi-pill"></i>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="card border-start border-3 border-{{ $activity['type'] === 'session' ? 'primary' : ($activity['type'] === 'assessment' ? 'success' : 'info') }}">
                                                <div class="card-body py-2">
                                                    <h6 class="mb-1">{{ $activity['title'] }}</h6>
                                                    <p class="text-muted mb-1">{{ $activity['description'] }}</p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <i class="bi bi-calendar me-1"></i>{{ $activity['date']->format('M d, Y') }}
                                                            @if(isset($activity['therapist']))
                                                                | <i class="bi bi-person me-1"></i>{{ $activity['therapist'] }}
                                                            @endif
                                                        </small>
                                                        @if(isset($activity['score']))
                                                            <span class="badge bg-secondary">Score: {{ $activity['score'] }}</span>
                                                        @endif
                                                    </div>
                                                    @if(isset($activity['outcome']))
                                                        <div class="mt-2">
                                                            <small class="text-success">
                                                                <i class="bi bi-check-circle me-1"></i>{{ $activity['outcome'] }}
                                                            </small>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!--begin::Risk Assessment Chart-->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Risk Assessment & Total Score</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <div class="display-4 fw-bold {{ $patient->total_risk_score > 2 ? 'text-danger' : ($patient->total_risk_score > 1 ? 'text-warning' : 'text-success') }}">
                                            {{ $patient->total_risk_score }}/5
                                        </div>
                                        <p class="text-muted">Total Risk Score</p>
                                        @if($patient->total_risk_score > 2)
                                            <div class="alert alert-danger">
                                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                <strong>RED FLAG ALERT!</strong><br>
                                                Immediate attention required
                                            </div>
                                        @elseif($patient->total_risk_score > 1)
                                            <div class="alert alert-warning">
                                                <i class="bi bi-exclamation-triangle me-2"></i>
                                                Monitor closely
                                            </div>
                                        @else
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                Risk assessment not completed
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <canvas id="riskChart" width="300" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    <!--begin::Patient Notes Section-->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-journal-medical me-2"></i>Patient Notes
                            </h5>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addNoteModal">
                                <i class="bi bi-plus-circle me-1"></i>Add New Note
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- Notes Filter Tabs -->
                            <ul class="nav nav-pills mb-3" id="notesTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="all-notes-tab" data-bs-toggle="pill" data-bs-target="#all-notes" type="button" role="tab">
                                        <i class="bi bi-journal-text me-1"></i>All Notes
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="session-notes-tab" data-bs-toggle="pill" data-bs-target="#session-notes" type="button" role="tab">
                                        <i class="bi bi-calendar-check me-1"></i>Session Notes
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="treatment-notes-tab" data-bs-toggle="pill" data-bs-target="#treatment-notes" type="button" role="tab">
                                        <i class="bi bi-heart-pulse me-1"></i>Treatment Notes
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="important-notes-tab" data-bs-toggle="pill" data-bs-target="#important-notes" type="button" role="tab">
                                        <i class="bi bi-star-fill me-1"></i>Important
                                    </button>
                                </li>
                            </ul>

                            <!-- Notes Content -->
                            <div class="tab-content" id="notesTabContent">
                                <!-- All Notes Tab -->
                                <div class="tab-pane fade show active" id="all-notes" role="tabpanel">
                                    <div id="notes-container">
                                        <!-- Sample notes - these would come from database -->
                                        <div class="note-item border rounded p-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">
                                                        <i class="bi bi-calendar-check me-1"></i>Session
                                                    </span>
                                                    <h6 class="mb-0">Initial Assessment Session</h6>
                                                </div>
                                                <div class="text-end">
                                                    <small class="text-muted">{{ now()->subDays(7)->format('M d, Y g:i A') }}</small>
                                                    <br>
                                                    <small class="text-muted">by Dr. {{ auth()->user()->name ?? 'John Doe' }}</small>
                                                </div>
                                            </div>
                                            <p class="mb-2">Patient presented with symptoms of anxiety and mild depression. Conducted initial assessment using standardized questionnaires. Patient is cooperative and willing to engage in treatment.</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-warning text-dark me-1">Follow-up required</span>
                                                    <span class="badge bg-secondary">anxiety</span>
                                                    <span class="badge bg-secondary">assessment</span>
                                                </div>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editNote(1)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteNote(1)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="note-item border rounded p-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">
                                                        <i class="bi bi-heart-pulse me-1"></i>Treatment
                                                    </span>
                                                    <h6 class="mb-0">Treatment Plan Discussion</h6>
                                                    <i class="bi bi-star-fill text-warning ms-2" title="Important"></i>
                                                </div>
                                                <div class="text-end">
                                                    <small class="text-muted">{{ now()->subDays(3)->format('M d, Y g:i A') }}</small>
                                                    <br>
                                                    <small class="text-muted">by Dr. {{ auth()->user()->name ?? 'Jane Smith' }}</small>
                                                </div>
                                            </div>
                                            <p class="mb-2">Discussed cognitive behavioral therapy approach. Patient agreed to weekly sessions. Prescribed relaxation techniques and mindfulness exercises. Patient showed good understanding of treatment goals.</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-secondary">CBT</span>
                                                    <span class="badge bg-secondary">treatment-plan</span>
                                                </div>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editNote(2)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteNote(2)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="note-item border rounded p-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">
                                                        <i class="bi bi-calendar-check me-1"></i>Session
                                                    </span>
                                                    <h6 class="mb-0">Progress Review Session</h6>
                                                </div>
                                                <div class="text-end">
                                                    <small class="text-muted">{{ now()->subDays(1)->format('M d, Y g:i A') }}</small>
                                                    <br>
                                                    <small class="text-muted">by Dr. {{ auth()->user()->name ?? 'John Doe' }}</small>
                                                </div>
                                            </div>
                                            <p class="mb-2">Patient reports significant improvement in anxiety levels. Sleep patterns have normalized. Continuing with current treatment approach. Next session scheduled for next week.</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-success">improvement</span>
                                                    <span class="badge bg-secondary">progress</span>
                                                </div>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editNote(3)">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteNote(3)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Session Notes Tab -->
                                <div class="tab-pane fade" id="session-notes" role="tabpanel">
                                    <div class="text-center py-4">
                                        <i class="bi bi-calendar-check display-4 text-muted"></i>
                                        <p class="text-muted mt-2">Session notes will be filtered and displayed here</p>
                                    </div>
                                </div>

                                <!-- Treatment Notes Tab -->
                                <div class="tab-pane fade" id="treatment-notes" role="tabpanel">
                                    <div class="text-center py-4">
                                        <i class="bi bi-heart-pulse display-4 text-muted"></i>
                                        <p class="text-muted mt-2">Treatment notes will be filtered and displayed here</p>
                                    </div>
                                </div>

                                <!-- Important Notes Tab -->
                                <div class="tab-pane fade" id="important-notes" role="tabpanel">
                                    <div class="text-center py-4">
                                        <i class="bi bi-star display-4 text-muted"></i>
                                        <p class="text-muted mt-2">Important notes will be filtered and displayed here</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Patient Notes Section-->

                </div>
            </div>
        </div>
    </div>
</main>

<!-- Add New Note Modal -->
<div class="modal fade" id="addNoteModal" tabindex="-1" aria-labelledby="addNoteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addNoteModalLabel">
                    <i class="bi bi-journal-plus me-2"></i>Add New Patient Note
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addNoteForm">
                @csrf
                <input type="hidden" name="patient_id" value="{{ $patient->id }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="note_type" class="form-label">Note Type</label>
                            <select class="form-select" id="note_type" name="note_type" required>
                                <option value="">Select note type...</option>
                                <option value="session">Session Note</option>
                                <option value="assessment">Assessment</option>
                                <option value="treatment">Treatment Note</option>
                                <option value="medication">Medication Note</option>
                                <option value="emergency">Emergency Note</option>
                                <option value="general">General Note</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="session_date" class="form-label">Session/Visit Date</label>
                            <input type="datetime-local" class="form-control" id="session_date" name="session_date" value="{{ now()->format('Y-m-d\TH:i') }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">Note Title</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="Brief title for this note..." required>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">Note Content</label>
                        <textarea class="form-control" id="content" name="content" rows="6" placeholder="Enter detailed note content here..." required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tags" class="form-label">Tags (comma-separated)</label>
                            <input type="text" class="form-control" id="tags" name="tags" placeholder="anxiety, progress, medication...">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="follow_up_date" class="form-label">Follow-up Date (optional)</label>
                            <input type="date" class="form-control" id="follow_up_date" name="follow_up_date">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_important" name="is_important">
                                <label class="form-check-label" for="is_important">
                                    <i class="bi bi-star text-warning me-1"></i>Mark as Important
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="follow_up_required" name="follow_up_required">
                                <label class="form-check-label" for="follow_up_required">
                                    <i class="bi bi-calendar-check text-info me-1"></i>Requires Follow-up
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-1"></i>Save Note
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function triggerAlert(patientId) {
    if (confirm('This will create a high-priority alert for this patient. Continue?')) {
        alert(`High-priority alert created for patient ID: ${patientId}. Relevant staff will be notified immediately.`);
        // In a real application, this would make an AJAX call to create the alert
    }
}

// Patient Notes Functions
function editNote(noteId) {
    // In a real application, this would load the note data and populate an edit modal
    alert('Edit note functionality - Note ID: ' + noteId);
}

function deleteNote(noteId) {
    if (confirm('Are you sure you want to delete this note? This action cannot be undone.')) {
        // In a real application, this would make an AJAX call to delete the note
        alert('Note deleted successfully');
        // Remove the note from the UI
        // location.reload(); // Refresh to update the notes list
    }
}

// Handle Add Note Form Submission
document.addEventListener('DOMContentLoaded', function() {
    const addNoteForm = document.getElementById('addNoteForm');
    if (addNoteForm) {
        addNoteForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Saving...';
            submitBtn.disabled = true;

            // Make AJAX call to save the note
            fetch('/patient-management/notes/store', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    try {
                        if (typeof toastr !== 'undefined' && toastr.success) {
                            toastr.success(data.message, 'Success');
                        } else {
                            alert(data.message);
                        }
                    } catch (error) {
                        alert(data.message);
                    }

                    // Close modal and reset form
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addNoteModal'));
                    modal.hide();
                    this.reset();

                    // Refresh the page to show the new note
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    throw new Error(data.message || 'Failed to save note');
                }
            })
            .catch(error => {
                console.error('Error saving note:', error);
                try {
                    if (typeof toastr !== 'undefined' && toastr.error) {
                        toastr.error('Failed to save note. Please try again.', 'Error');
                    } else {
                        alert('Failed to save note. Please try again.');
                    }
                } catch (e) {
                    alert('Failed to save note. Please try again.');
                }
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // Handle follow-up checkbox change
    const followUpCheckbox = document.getElementById('follow_up_required');
    const followUpDateField = document.getElementById('follow_up_date');

    if (followUpCheckbox && followUpDateField) {
        followUpCheckbox.addEventListener('change', function() {
            if (this.checked) {
                followUpDateField.required = true;
                followUpDateField.parentElement.classList.add('required');
            } else {
                followUpDateField.required = false;
                followUpDateField.parentElement.classList.remove('required');
            }
        });
    }
});

// Risk Assessment Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('riskChart').getContext('2d');
    const riskScore = {{ $patient->total_risk_score }};

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Risk Score', 'Remaining'],
            datasets: [{
                data: [riskScore, 5 - riskScore],
                backgroundColor: [
                    riskScore > 2 ? '#dc3545' : (riskScore > 1 ? '#ffc107' : '#198754'),
                    '#e9ecef'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.dataIndex === 0) {
                                return 'Risk Score: ' + riskScore + '/5';
                            }
                            return '';
                        }
                    }
                }
            },
            cutout: '70%'
        }
    });
});
</script>

<style>
.note-item {
    transition: all 0.3s ease;
    border-left: 4px solid #e9ecef;
}

.note-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-left-color: #0d6efd;
}

.note-item .badge {
    font-size: 0.75rem;
}

.nav-pills .nav-link {
    border-radius: 20px;
    margin-right: 5px;
}

.nav-pills .nav-link.active {
    background-color: #0d6efd;
}

.required::after {
    content: " *";
    color: red;
}

#notes-container {
    max-height: 600px;
    overflow-y: auto;
}

.modal-lg {
    max-width: 800px;
}

.form-check-label {
    cursor: pointer;
}

.note-item .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
@endsection
