<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PatientManagement extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'DOB',
        'gender',
        'status',
        'address',
        'next_of_keen',
        'study_id',
        'site',
        'clinic',
        'hospital_id',
        'assigned_therapist_id',
        'assignment_date',
        'assignment_notes',
        'diagnosis',
        'risk_level',
        'treatment_progress',
        'core10_score',
        'wai_score',
        'satisfaction_score',
        'total_risk_score',
        'next_appointment',
        'total_sessions',
        'last_session_date',
        'insurance_provider',
        'insurance_policy_number',
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_relationship',
        'treatment_preferences',
        'medical_history',
        'current_medications',
        'treatment_goals',
        'therapist_notes',
        'treatment_status',
        'is_high_priority',
        'status_updated_at',
    ];

    protected $casts = [
        'DOB' => 'date',
        'assignment_date' => 'date',
        'next_appointment' => 'datetime',
        'last_session_date' => 'datetime',
        'status_updated_at' => 'datetime',
        'treatment_preferences' => 'array',
        'core10_score' => 'decimal:1',
        'wai_score' => 'decimal:1',
        'total_risk_score' => 'decimal:2',
        'is_high_priority' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function assignedTherapist()
    {
        return $this->belongsTo(TherapistManagement::class, 'assigned_therapist_id');
    }

    public function clinicRecord()
    {
        return $this->belongsTo(ClinicManagement::class, 'clinic', 'name');
    }

    public function hospital()
    {
        return $this->belongsTo(ClinicManagement::class, 'hospital_id');
    }

    public function notes()
    {
        return $this->hasMany(PatientNote::class, 'patient_id')->orderBy('created_at', 'desc');
    }

    // Computed attributes
    public function getAgeAttribute()
    {
        return $this->DOB ? $this->DOB->age : null;
    }

    public function getRegistrationDateAttribute()
    {
        return $this->created_at;
    }

    public function getFormattedDobAttribute()
    {
        return $this->DOB ? $this->DOB->format('M d, Y') : 'Not provided';
    }

    public function getFormattedRegistrationDateAttribute()
    {
        return $this->created_at ? $this->created_at->format('M d, Y') : 'Not available';
    }

    public function getFormattedNextAppointmentAttribute()
    {
        return $this->next_appointment ? $this->next_appointment->format('M d, Y g:i A') : 'Not scheduled';
    }

    public function getFormattedLastSessionAttribute()
    {
        return $this->last_session_date ? $this->last_session_date->format('M d, Y') : 'No sessions yet';
    }

    public function getRiskLevelColorAttribute()
    {
        return match($this->risk_level) {
            'High' => 'danger',
            'Medium' => 'warning',
            'Low' => 'success',
            default => 'secondary'
        };
    }

    public function getTreatmentStatusColorAttribute()
    {
        return match($this->treatment_status) {
            'Active' => 'success',
            'On Hold' => 'warning',
            'Completed' => 'primary',
            'Discontinued' => 'danger',
            default => 'secondary'
        };
    }

    // Helper methods
    public function assignToTherapist($therapistId, $notes = null)
    {
        $this->update([
            'assigned_therapist_id' => $therapistId,
            'assignment_date' => now(),
            'assignment_notes' => $notes,
            'status_updated_at' => now()
        ]);
    }

    public function updateTreatmentProgress($progress)
    {
        $this->update([
            'treatment_progress' => max(0, min(100, $progress)),
            'status_updated_at' => now()
        ]);
    }

    public function scheduleNextAppointment($datetime, $notes = null)
    {
        $this->update([
            'next_appointment' => $datetime,
            'therapist_notes' => $notes ? ($this->therapist_notes . "\n" . now()->format('Y-m-d') . ": " . $notes) : $this->therapist_notes
        ]);
    }

    public function recordSession($notes = null)
    {
        $this->increment('total_sessions');
        $this->update([
            'last_session_date' => now(),
            'therapist_notes' => $notes ? ($this->therapist_notes . "\n" . now()->format('Y-m-d') . ": " . $notes) : $this->therapist_notes
        ]);
    }

    public function updateRiskLevel($level, $score = null)
    {
        $this->update([
            'risk_level' => $level,
            'total_risk_score' => $score,
            'is_high_priority' => $level === 'High',
            'status_updated_at' => now()
        ]);
    }

    // Scopes
    public function scopeAssigned($query)
    {
        return $query->whereNotNull('assigned_therapist_id');
    }

    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_therapist_id');
    }

    public function scopeHighRisk($query)
    {
        return $query->where('risk_level', 'High');
    }

    public function scopeActivePatients($query)
    {
        return $query->where('treatment_status', 'Active');
    }

    public function scopeByTherapist($query, $therapistId)
    {
        return $query->where('assigned_therapist_id', $therapistId);
    }

    public function scopeUpcomingAppointments($query, $days = 7)
    {
        return $query->whereBetween('next_appointment', [
            now(),
            now()->addDays($days)
        ]);
    }
}
