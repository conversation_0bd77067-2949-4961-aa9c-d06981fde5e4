<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DiagnosisCategory;

class DiagnosisCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'code' => 'MD',
                'name' => 'Mood Disorders',
                'description' => 'Disorders primarily affecting mood and emotional state',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'code' => 'AD',
                'name' => 'Anxiety Disorders',
                'description' => 'Disorders characterized by excessive fear, anxiety, and related behavioral disturbances',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'PD',
                'name' => 'Personality Disorders',
                'description' => 'Enduring patterns of inner experience and behavior that deviate from cultural expectations',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'code' => 'SD',
                'name' => 'Substance-Related Disorders',
                'description' => 'Disorders related to substance use and addiction',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'code' => 'TD',
                'name' => 'Trauma and Stressor-Related Disorders',
                'description' => 'Disorders following exposure to traumatic or stressful events',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'code' => 'ED',
                'name' => 'Eating Disorders',
                'description' => 'Disorders characterized by persistent disturbance of eating behavior',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'code' => 'SLD',
                'name' => 'Sleep-Wake Disorders',
                'description' => 'Disorders involving problems with the timing, quality, or amount of sleep',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'code' => 'NCD',
                'name' => 'Neurocognitive Disorders',
                'description' => 'Disorders characterized by decline in cognitive function',
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'code' => 'CD',
                'name' => 'Childhood Disorders',
                'description' => 'Disorders typically first diagnosed in infancy, childhood, or adolescence',
                'is_active' => true,
                'sort_order' => 9,
            ],
            [
                'code' => 'OTH',
                'name' => 'Other Mental Health Conditions',
                'description' => 'Other specified and unspecified mental health conditions',
                'is_active' => true,
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $category) {
            DiagnosisCategory::firstOrCreate(
                ['code' => $category['code']],
                $category
            );
        }

        $this->command->info('Diagnosis categories seeded successfully!');
    }
}
