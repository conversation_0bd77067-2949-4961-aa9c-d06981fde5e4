<?php

return [
    /*
    |--------------------------------------------------------------------------
    | System Permissions Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains all the permissions available in the Marbar-Africa
    | healthcare management system. Permissions are organized by modules
    | and groups for better management and assignment.
    |
    */

    'permissions' => [
        // User Management Permissions
        'user_management' => [
            'module' => 'users',
            'permissions' => [
                'users.view' => [
                    'name' => 'View Users',
                    'description' => 'View user list and user details',
                ],
                'users.create' => [
                    'name' => 'Create Users',
                    'description' => 'Create new user accounts',
                ],
                'users.edit' => [
                    'name' => 'Edit Users',
                    'description' => 'Edit existing user information',
                ],
                'users.delete' => [
                    'name' => 'Delete Users',
                    'description' => 'Delete user accounts',
                ],
                'users.assign_roles' => [
                    'name' => 'Assign User Roles',
                    'description' => 'Assign and manage user roles',
                ],
                'users.manage_permissions' => [
                    'name' => 'Manage User Permissions',
                    'description' => 'Grant or revoke specific permissions to users',
                ],
                'users.export' => [
                    'name' => 'Export User Data',
                    'description' => 'Export user data and reports',
                ],
            ],
        ],

        // Patient Management Permissions
        'patient_management' => [
            'module' => 'patients',
            'permissions' => [
                'patients.view' => [
                    'name' => 'View Patients',
                    'description' => 'View patient list and patient details',
                ],
                'patients.view_assigned' => [
                    'name' => 'View Assigned Patients',
                    'description' => 'View only patients assigned to the current therapist',
                ],
                'patients.create' => [
                    'name' => 'Register Patients',
                    'description' => 'Register new patients in the system',
                ],
                'patients.edit' => [
                    'name' => 'Edit Patient Information',
                    'description' => 'Edit existing patient information',
                ],
                'patients.delete' => [
                    'name' => 'Delete Patient Records',
                    'description' => 'Delete patient records (restricted)',
                ],
                'patients.medical_history' => [
                    'name' => 'View Medical History',
                    'description' => 'Access patient medical history',
                ],
                'patients.assign_therapist' => [
                    'name' => 'Assign Therapists',
                    'description' => 'Assign therapists to patients',
                ],
                'patients.export' => [
                    'name' => 'Export Patient Data',
                    'description' => 'Export patient data and reports',
                ],
                'patients.analytics' => [
                    'name' => 'Patient Analytics',
                    'description' => 'View patient analytics and demographics',
                ],
            ],
        ],

        // Therapist Management Permissions
        'therapist_management' => [
            'module' => 'therapists',
            'permissions' => [
                'therapists.view' => [
                    'name' => 'View Therapists',
                    'description' => 'View therapist directory and profiles',
                ],
                'therapists.create' => [
                    'name' => 'Add Therapists',
                    'description' => 'Add new therapists to the system',
                ],
                'therapists.edit' => [
                    'name' => 'Edit Therapist Profiles',
                    'description' => 'Edit therapist information and profiles',
                ],
                'therapists.delete' => [
                    'name' => 'Remove Therapists',
                    'description' => 'Remove therapists from the system',
                ],
                'therapists.assign_patients' => [
                    'name' => 'Assign Patients',
                    'description' => 'Assign patients to therapists',
                ],
                'therapists.view_constellation' => [
                    'name' => 'View Patient Constellation',
                    'description' => 'View therapist patient assignments',
                ],
                'therapists.manage_schedule' => [
                    'name' => 'Manage Schedules',
                    'description' => 'Manage therapist schedules and availability',
                ],
                'therapists.analytics' => [
                    'name' => 'Therapist Analytics',
                    'description' => 'View therapist performance analytics',
                ],
            ],
        ],

        // Clinic Management Permissions
        'clinic_management' => [
            'module' => 'clinics',
            'permissions' => [
                'clinics.view' => [
                    'name' => 'View Clinics',
                    'description' => 'View clinic list and clinic details',
                ],
                'clinics.create' => [
                    'name' => 'Create Clinics',
                    'description' => 'Register new clinics in the system',
                ],
                'clinics.edit' => [
                    'name' => 'Edit Clinic Information',
                    'description' => 'Edit existing clinic information',
                ],
                'clinics.delete' => [
                    'name' => 'Delete Clinics',
                    'description' => 'Delete clinic records',
                ],
                'clinics.manage_staff' => [
                    'name' => 'Manage Clinic Staff',
                    'description' => 'Manage clinic staff assignments',
                ],
                'clinics.manage_services' => [
                    'name' => 'Manage Services',
                    'description' => 'Manage clinic services and offerings',
                ],
                'clinics.operational_history' => [
                    'name' => 'View Operational History',
                    'description' => 'View clinic operational history',
                ],
                'clinics.analytics' => [
                    'name' => 'Clinic Analytics',
                    'description' => 'View clinic performance analytics',
                ],
            ],
        ],

        // Researcher Management Permissions
        'researcher_management' => [
            'module' => 'researchers',
            'permissions' => [
                'researchers.view' => [
                    'name' => 'View Researchers',
                    'description' => 'View researcher directory and profiles',
                ],
                'researchers.create' => [
                    'name' => 'Add Researchers',
                    'description' => 'Add new researchers to the system',
                ],
                'researchers.edit' => [
                    'name' => 'Edit Researcher Profiles',
                    'description' => 'Edit researcher information and credentials',
                ],
                'researchers.delete' => [
                    'name' => 'Remove Researchers',
                    'description' => 'Remove researchers from the system',
                ],
                'researchers.manage_projects' => [
                    'name' => 'Manage Research Projects',
                    'description' => 'Create and manage research projects',
                ],
                'researchers.manage_publications' => [
                    'name' => 'Manage Publications',
                    'description' => 'Manage research publications',
                ],
                'researchers.analytics' => [
                    'name' => 'Research Analytics',
                    'description' => 'View research analytics and metrics',
                ],
            ],
        ],

        // Diagnoses Management Permissions
        'diagnoses_management' => [
            'module' => 'diagnoses',
            'permissions' => [
                'diagnoses.view' => [
                    'name' => 'View Diagnoses',
                    'description' => 'View diagnoses list and diagnosis details',
                ],
                'diagnoses.create' => [
                    'name' => 'Add Diagnoses',
                    'description' => 'Add new diagnoses to the system',
                ],
                'diagnoses.edit' => [
                    'name' => 'Edit Diagnoses',
                    'description' => 'Edit existing diagnosis information',
                ],
                'diagnoses.delete' => [
                    'name' => 'Delete Diagnoses',
                    'description' => 'Delete diagnosis records',
                ],
                'diagnoses.categories' => [
                    'name' => 'Manage Diagnosis Categories',
                    'description' => 'Manage diagnosis categories and classifications',
                ],
                'diagnoses.analytics' => [
                    'name' => 'Diagnosis Analytics',
                    'description' => 'View diagnosis analytics and reports',
                ],
            ],
        ],

        // Role & Permission Management
        'role_management' => [
            'module' => 'roles',
            'permissions' => [
                'roles.view' => [
                    'name' => 'View Roles',
                    'description' => 'View system roles and permissions',
                ],
                'roles.create' => [
                    'name' => 'Create Roles',
                    'description' => 'Create new system roles',
                ],
                'roles.edit' => [
                    'name' => 'Edit Roles',
                    'description' => 'Edit existing roles and permissions',
                ],
                'roles.delete' => [
                    'name' => 'Delete Roles',
                    'description' => 'Delete custom roles (not system roles)',
                ],
                'permissions.view' => [
                    'name' => 'View Permissions',
                    'description' => 'View system permissions',
                ],
                'permissions.create' => [
                    'name' => 'Create Permissions',
                    'description' => 'Create custom permissions',
                ],
                'permissions.edit' => [
                    'name' => 'Edit Permissions',
                    'description' => 'Edit permission details',
                ],
                'permissions.delete' => [
                    'name' => 'Delete Permissions',
                    'description' => 'Delete custom permissions',
                ],
            ],
        ],

        // System Administration
        'system_administration' => [
            'module' => 'system',
            'permissions' => [
                'system.dashboard' => [
                    'name' => 'System Dashboard',
                    'description' => 'Access system administration dashboard',
                ],
                'system.settings' => [
                    'name' => 'System Settings',
                    'description' => 'Manage system configuration and settings',
                ],
                'system.maintenance' => [
                    'name' => 'System Maintenance',
                    'description' => 'Perform system maintenance tasks',
                ],
                'system.backup' => [
                    'name' => 'System Backup',
                    'description' => 'Create and manage system backups',
                ],
                'system.logs' => [
                    'name' => 'View System Logs',
                    'description' => 'View and manage system logs',
                ],
                'system.audit' => [
                    'name' => 'Audit Trail',
                    'description' => 'View system audit trail and user activities',
                ],
            ],
        ],

        // Analytics & Reporting
        'analytics' => [
            'module' => 'analytics',
            'permissions' => [
                'analytics.dashboard' => [
                    'name' => 'Analytics Dashboard',
                    'description' => 'Access analytics dashboard',
                ],
                'analytics.reports' => [
                    'name' => 'Generate Reports',
                    'description' => 'Generate and view system reports',
                ],
                'analytics.export' => [
                    'name' => 'Export Analytics',
                    'description' => 'Export analytics data and reports',
                ],
                'analytics.advanced' => [
                    'name' => 'Advanced Analytics',
                    'description' => 'Access advanced analytics features',
                ],
            ],
        ],

        // Communication & Notifications
        'communication' => [
            'module' => 'notifications',
            'permissions' => [
                'notifications.view' => [
                    'name' => 'View Notifications',
                    'description' => 'View system notifications',
                ],
                'notifications.send' => [
                    'name' => 'Send Notifications',
                    'description' => 'Send notifications to users',
                ],
                'notifications.manage' => [
                    'name' => 'Manage Notifications',
                    'description' => 'Manage notification settings and templates',
                ],
                'communication.contact' => [
                    'name' => 'Contact Management',
                    'description' => 'Manage contact forms and communications',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Role Permissions
    |--------------------------------------------------------------------------
    |
    | Define which permissions each default role should have.
    |
    */
    'default_role_permissions' => [
        'superadmin' => '*', // All permissions (including patients.view_assigned)
        'admin' => [
            'users.view', 'users.create', 'users.edit', 'users.assign_roles', 'users.export',
            'patients.view', 'patients.create', 'patients.edit', 'patients.medical_history', 'patients.assign_therapist', 'patients.export', 'patients.analytics',
            'therapists.view', 'therapists.create', 'therapists.edit', 'therapists.assign_patients', 'therapists.view_constellation', 'therapists.analytics',
            'clinics.view', 'clinics.create', 'clinics.edit', 'clinics.manage_staff', 'clinics.manage_services', 'clinics.analytics',
            'researchers.view', 'researchers.create', 'researchers.edit', 'researchers.analytics',
            'diagnoses.view', 'diagnoses.create', 'diagnoses.edit', 'diagnoses.categories', 'diagnoses.analytics',
            'roles.view', 'permissions.view',
            'analytics.dashboard', 'analytics.reports', 'analytics.export',
            'notifications.view', 'notifications.send', 'notifications.manage',
            'system.dashboard', 'system.logs',
        ],
        'therapist' => [
            'patients.view', 'patients.medical_history', 'patients.analytics',
            'therapists.view', 'therapists.view_constellation',
            'clinics.view', 'analytics.dashboard', 'notifications.view',
            'diagnoses.view', 'diagnoses.analytics',
        ],
        'therapist_assigned_only' => [
            'patients.view_assigned', 'patients.medical_history', 'patients.analytics',
            'therapists.view', 'therapists.view_constellation',
            'clinics.view', 'analytics.dashboard', 'notifications.view',
            'diagnoses.view', 'diagnoses.analytics',
        ],
        'researcher' => [
            'patients.view', 'patients.analytics', 'therapists.view', 'clinics.view',
            'researchers.view', 'researchers.manage_projects', 'researchers.manage_publications',
            'researchers.analytics', 'analytics.*',
            'diagnoses.view', 'diagnoses.analytics',
        ],
        'patient' => [
            'notifications.view',
        ],
        'user' => [
            'notifications.view',
        ],
    ],
];
