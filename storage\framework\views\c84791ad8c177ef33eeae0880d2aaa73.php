<?php $__env->startSection('content'); ?>
<style>
.blink {
    animation: blink-animation 1s steps(5, start) infinite;
}

@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

.risk-indicator {
    position: relative;
}

.risk-indicator.high-risk::after {
    content: "⚠️";
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.card.high-risk-patient {
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.3);
    border: 2px solid rgba(220, 53, 69, 0.5) !important;
}
</style>
<!--begin::App Main-->
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Patient Registry</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('patient-management.dashboard')); ?>">Patient Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Registry</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!--begin::App Content-->
    <div class="app-content">
        <div class="container-fluid">

            <!--begin::Statistics Overview-->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2">
                                    <div class="h3 mb-1"><?php echo e($registryStats['total_patients']); ?></div>
                                    <small>Total Patients</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1"><?php echo e($registryStats['active_count']); ?></div>
                                    <small>Active</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1"><?php echo e($registryStats['new_this_month']); ?></div>
                                    <small>New This Month</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1"><?php echo e($registryStats['high_risk_count']); ?></div>
                                    <small>High Risk</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1"><?php echo e($registryStats['medium_risk_count']); ?></div>
                                    <small>Medium Risk</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="h3 mb-1"><?php echo e($registryStats['low_risk_count']); ?></div>
                                    <small>Low Risk</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--begin::Search and Filters-->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('patient-management.registry.index')); ?>" id="filterForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="search" class="form-label">Search Patients</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" id="search"
                                               value="<?php echo e(request('search')); ?>" placeholder="Name, email, or ID...">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" name="status" id="status">
                                        <option value="">All Status</option>
                                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                        <option value="discharged" <?php echo e(request('status') == 'discharged' ? 'selected' : ''); ?>>Discharged</option>
                                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="risk_level" class="form-label">Risk Level</label>
                                    <select class="form-select" name="risk_level" id="risk_level">
                                        <option value="">All Risk Levels</option>
                                        <option value="High" <?php echo e(request('risk_level') == 'High' ? 'selected' : ''); ?>>High Risk</option>
                                        <option value="Medium" <?php echo e(request('risk_level') == 'Medium' ? 'selected' : ''); ?>>Medium Risk</option>
                                        <option value="Low" <?php echo e(request('risk_level') == 'Low' ? 'selected' : ''); ?>>Low Risk</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="diagnosis" class="form-label">Diagnosis</label>
                                    <select class="form-select" name="diagnosis" id="diagnosis">
                                        <option value="">All Diagnoses</option>
                                        <option value="Anxiety" <?php echo e(request('diagnosis') == 'Anxiety' ? 'selected' : ''); ?>>Anxiety Disorder</option>
                                        <option value="Depression" <?php echo e(request('diagnosis') == 'Depression' ? 'selected' : ''); ?>>Depression</option>
                                        <option value="PTSD" <?php echo e(request('diagnosis') == 'PTSD' ? 'selected' : ''); ?>>PTSD</option>
                                        <option value="Bipolar" <?php echo e(request('diagnosis') == 'Bipolar' ? 'selected' : ''); ?>>Bipolar Disorder</option>
                                        <option value="Addiction" <?php echo e(request('diagnosis') == 'Addiction' ? 'selected' : ''); ?>>Addiction</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="therapist" class="form-label">Therapist</label>
                                    <select class="form-select" name="therapist" id="therapist">
                                        <option value="">All Therapists</option>
                                        <option value="Dr. Amina Hassan" <?php echo e(request('therapist') == 'Dr. Amina Hassan' ? 'selected' : ''); ?>>Dr. Amina Hassan</option>
                                        <option value="Dr. Grace Wanjiku" <?php echo e(request('therapist') == 'Dr. Grace Wanjiku' ? 'selected' : ''); ?>>Dr. Grace Wanjiku</option>
                                        <option value="Dr. James Mwangi" <?php echo e(request('therapist') == 'Dr. James Mwangi' ? 'selected' : ''); ?>>Dr. James Mwangi</option>
                                        <option value="Dr. Sarah Johnson" <?php echo e(request('therapist') == 'Dr. Sarah Johnson' ? 'selected' : ''); ?>>Dr. Sarah Johnson</option>
                                        <option value="Dr. Michael Ochieng" <?php echo e(request('therapist') == 'Dr. Michael Ochieng' ? 'selected' : ''); ?>>Dr. Michael Ochieng</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-funnel me-2"></i>Apply Filters
                                </button>
                                <a href="<?php echo e(route('patient-management.registry.index')); ?>" class="btn btn-outline-secondary me-2">
                                    <i class="bi bi-x-circle me-2"></i>Clear Filters
                                </a>
                                <a href="<?php echo e(route('patient-management.registry.create')); ?>" class="btn btn-success me-2">
                                    <i class="bi bi-person-plus me-2"></i>Add New Patient
                                </a>
                                <button type="button" class="btn btn-info" onclick="exportPatients()">
                                    <i class="bi bi-download me-2"></i>Export Data
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!--begin::Patient List-->
            <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $patients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $patient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-6 col-xl-4">
                    <div class="card mb-4 border-start border-<?php echo e($patient->risk_level === 'High' ? 'danger' : ($patient->risk_level === 'Medium' ? 'warning' : 'success')); ?> border-4 <?php echo e(($patient->total_risk_score > 2) ? 'high-risk-patient' : ''); ?>">
                        <div class="card-body">
                            <div class="row align-items-center mb-3">
                                <div class="col-auto">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                        <?php echo e(substr($patient->name, 0, 1)); ?><?php echo e(substr(explode(' ', $patient->name)[1] ?? '', 0, 1)); ?>

                                    </div>
                                </div>
                                <div class="col">
                                    <h5 class="mb-1"><?php echo e($patient->name); ?></h5>
                                    <p class="text-muted mb-1"><?php echo e($patient->email ?? 'No email provided'); ?></p>
                                    <div class="text-warning">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= floor($patient->satisfaction_score)): ?>
                                                <i class="bi bi-star-fill"></i>
                                            <?php elseif($i <= $patient->satisfaction_score): ?>
                                                <i class="bi bi-star-half"></i>
                                            <?php else: ?>
                                                <i class="bi bi-star"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                        <small class="text-muted ms-1">(<?php echo e($patient->satisfaction_score); ?>)</small>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <?php
                                        $statusClass = match(strtolower($patient->status)) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-secondary',
                                            default => 'bg-danger'
                                        };
                                        $riskClass = match(strtolower($patient->risk_level)) {
                                            'high' => 'bg-danger',
                                            'medium' => 'bg-warning',
                                            default => 'bg-success'
                                        };
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?> mb-1"><?php echo e(ucfirst($patient->status)); ?></span><br>
                                    <span class="badge <?php echo e($riskClass); ?>"><?php echo e($patient->risk_level); ?> Risk</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <span class="badge bg-primary me-1"><?php echo e($patient->diagnosis); ?></span>
                                <span class="badge bg-light text-dark me-1">Age: <?php echo e($patient->age ?? 'Unknown'); ?></span>
                                <span class="badge bg-light text-dark me-1"><?php echo e($patient->gender); ?></span>
                                <?php if($patient->total_risk_score > 2): ?>
                                    <span class="badge bg-danger me-1 blink">🚩 RED FLAG</span>
                                <?php endif; ?>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary"><?php echo e($patient->treatment_progress); ?>%</div>
                                    <small class="text-muted">Progress</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success"><?php echo e($patient->created_at->diffInMonths()); ?>m</div>
                                    <small class="text-muted">Duration</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info">N/A</div>
                                    <small class="text-muted">Next Appt</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-person-badge me-1"></i><?php echo e($patient->assigned_therapist); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-telephone me-1"></i><?php echo e($patient->phone_number ?? 'No phone'); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-calendar-plus me-1"></i>DOB: <?php echo e($patient->formatted_dob); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt me-1"></i><?php echo e($patient->address ?? 'Address not provided'); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-calendar-check me-1"></i>Reg: <?php echo e($patient->formatted_registration_date); ?>

                                </small><br>
                                <small class="text-muted">
                                    <i class="bi bi-building me-1"></i>Site: <?php echo e($patient->site); ?> | Clinic: <?php echo e($patient->clinic); ?>

                                </small><br>
                            </div>

                            <div class="d-flex flex-wrap gap-2">
                                <a href="<?php echo e(route('patient-management.registry.view', $patient->id)); ?>" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <a href="<?php echo e(route('patient-management.registry.edit', $patient->id)); ?>" class="btn btn-sm btn-success">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a href="<?php echo e(route('patient-management.registry.medical-history', $patient->id)); ?>" class="btn btn-sm btn-primary">
                                    <i class="bi bi-file-medical"></i> History
                                </a>
                                <?php if($patient->risk_level === 'High' || $patient->total_risk_score > 2): ?>
                                <button class="btn btn-sm btn-danger" onclick="flagHighRisk(<?php echo e($patient->id); ?>)">
                                    <i class="bi bi-flag"></i> Alert
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No Patients Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or add a new patient.</p>
                        <a href="<?php echo e(route('patient-management.registry.create')); ?>" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add New Patient
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!--begin::Pagination-->
            <?php if($patients->hasPages()): ?>
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <p class="text-muted mb-0">
                        Showing <?php echo e($patients->firstItem()); ?> to <?php echo e($patients->lastItem()); ?> of <?php echo e($patients->total()); ?> patients
                    </p>
                </div>
                <div>
                    <?php echo e($patients->appends(request()->query())->links('custom.pagination')); ?>

                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>
</main>

<script>
function exportPatients() {
    alert('Exporting patient data... (Feature coming soon!)');
}

function flagHighRisk(patientId) {
    if (confirm('This will create a HIGH-PRIORITY ALERT for this patient and notify all relevant staff immediately. Continue?')) {
        // Show loading state
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating Alert...';
        button.disabled = true;

        // Simulate alert creation
        setTimeout(() => {
            alert(`🚨 HIGH-PRIORITY ALERT CREATED!\n\nPatient ID: ${patientId}\nAlert Type: Red Flag Risk Assessment\nStatus: Active\n\nNotifications sent to:\n- Assigned Therapist\n- Clinical Supervisor\n- Emergency Response Team\n\nImmediate action required within 24 hours.`);

            // Reset button
            button.innerHTML = '<i class="bi bi-check-circle"></i> Alert Sent';
            button.classList.remove('btn-danger');
            button.classList.add('btn-success');

            // Re-enable after 3 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-danger');
                button.disabled = false;
            }, 3000);
        }, 1500);
    }
}

// Auto-submit form on filter change for better UX
document.addEventListener('DOMContentLoaded', function() {
    const filterSelects = document.querySelectorAll('#status, #risk_level, #diagnosis, #therapist');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });

    // Add real-time search suggestions (optional enhancement)
    const searchInput = document.getElementById('search');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value;

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // This could be enhanced to show search suggestions
                console.log('Searching for:', query);
                // Uncomment below to implement AJAX search suggestions
                // fetchSearchSuggestions(query);
            }, 300);
        }
    });
});

// Optional: Implement AJAX search suggestions
function fetchSearchSuggestions(query) {
    fetch(`<?php echo e(route('patient-management.registry.search')); ?>?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            console.log('Search results:', data);
            // Here you could display search suggestions in a dropdown
        })
        .catch(error => {
            console.error('Search error:', error);
        });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/management/patient/registry/index.blade.php ENDPATH**/ ?>