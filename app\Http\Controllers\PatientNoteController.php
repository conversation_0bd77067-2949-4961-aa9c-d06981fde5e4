<?php

namespace App\Http\Controllers;

use App\Models\PatientNote;
use App\Models\PatientManagement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PatientNoteController extends Controller
{
    /**
     * Store a new patient note.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'patient_id' => 'required|exists:patient_management,id',
            'note_type' => 'required|in:session,assessment,treatment,medication,emergency,general',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'session_date' => 'nullable|date',
            'tags' => 'nullable|string',
            'follow_up_date' => 'nullable|date',
            'is_important' => 'boolean',
            'follow_up_required' => 'boolean',
            'is_private' => 'boolean',
        ]);

        // Process tags
        $tags = null;
        if ($request->tags) {
            $tags = array_map('trim', explode(',', $request->tags));
            $tags = array_filter($tags); // Remove empty tags
        }

        $note = PatientNote::create([
            'patient_id' => $validatedData['patient_id'],
            'user_id' => Auth::id(),
            'note_type' => $validatedData['note_type'],
            'title' => $validatedData['title'],
            'content' => $validatedData['content'],
            'session_date' => $validatedData['session_date'] ?? now(),
            'tags' => $tags,
            'follow_up_date' => $validatedData['follow_up_date'],
            'is_important' => $request->boolean('is_important'),
            'follow_up_required' => $request->boolean('follow_up_required'),
            'is_private' => $request->boolean('is_private'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Note added successfully',
            'note' => $note->load('author')
        ]);
    }

    /**
     * Get notes for a specific patient.
     */
    public function getPatientNotes($patientId, Request $request)
    {
        $patient = PatientManagement::findOrFail($patientId);
        
        $query = $patient->notes()->with('author');

        // Filter by note type if specified
        if ($request->has('type') && $request->type !== 'all') {
            $query->where('note_type', $request->type);
        }

        // Filter by important notes if specified
        if ($request->has('important') && $request->important) {
            $query->where('is_important', true);
        }

        $notes = $query->paginate(10);

        return response()->json([
            'success' => true,
            'notes' => $notes
        ]);
    }

    /**
     * Update a patient note.
     */
    public function update(Request $request, PatientNote $note)
    {
        $validatedData = $request->validate([
            'note_type' => 'required|in:session,assessment,treatment,medication,emergency,general',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'session_date' => 'nullable|date',
            'tags' => 'nullable|string',
            'follow_up_date' => 'nullable|date',
            'is_important' => 'boolean',
            'follow_up_required' => 'boolean',
            'is_private' => 'boolean',
        ]);

        // Process tags
        $tags = null;
        if ($request->tags) {
            $tags = array_map('trim', explode(',', $request->tags));
            $tags = array_filter($tags);
        }

        $note->update([
            'note_type' => $validatedData['note_type'],
            'title' => $validatedData['title'],
            'content' => $validatedData['content'],
            'session_date' => $validatedData['session_date'],
            'tags' => $tags,
            'follow_up_date' => $validatedData['follow_up_date'],
            'is_important' => $request->boolean('is_important'),
            'follow_up_required' => $request->boolean('follow_up_required'),
            'is_private' => $request->boolean('is_private'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Note updated successfully',
            'note' => $note->load('author')
        ]);
    }

    /**
     * Delete a patient note.
     */
    public function destroy(PatientNote $note)
    {
        $note->delete();

        return response()->json([
            'success' => true,
            'message' => 'Note deleted successfully'
        ]);
    }

    /**
     * Get a specific note for editing.
     */
    public function show(PatientNote $note)
    {
        return response()->json([
            'success' => true,
            'note' => $note->load('author')
        ]);
    }
}
