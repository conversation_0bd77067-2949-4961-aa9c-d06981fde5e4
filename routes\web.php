<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PatientManagementController;
use App\Http\Controllers\PatientNoteController;
use App\Http\Controllers\TherapistManagementController;
use App\Http\Controllers\ClinicManagementController;
use App\Http\Controllers\ResearcherManagementController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\RoleManagementController;
use App\Http\Controllers\PermissionManagementController;
use App\Http\Controllers\UserRoleAssignmentController;
use App\Http\Controllers\SystemLogsController;
use App\Http\Controllers\DiagnosesManagementController;
use App\Http\Controllers\LockScreenController;
use Illuminate\Support\Facades\Route;

// Test route to check if <PERSON><PERSON> is working
Route::get('/test-basic', function () {
    return response()->json(['status' => 'Laravel is working', 'time' => now()]);
});

Route::get('/', [AdminController::class, 'dashboard'])->middleware(['auth', 'verified'])->name('home');

Route::get('/login', function () {
    return view('auth.login');
})->name('login');

Route::get('/register', function () {
    return view('auth.register');
})->name('register');

Route::get('/dashboard', [AdminController::class, 'dashboard'])->middleware(['auth', 'verified'])->name('dashboard');

// Legacy add patient route - redirects to new location
Route::get('/add-patient', function () {
    return redirect()->route('patient-management.registry.create');
})->name('add.patient.legacy');

// Session destroy route - outside screen.lock middleware to prevent lock screen redirect on logout
Route::get('/destroy', [AdminController::class, 'destroy'])->middleware(['auth'])->name('session.destroy');

Route::middleware(['auth', 'screen.lock'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Search routes
    Route::get('/search', [SearchController::class, 'index'])->name('search.index');
    Route::get('/search/ajax', [SearchController::class, 'ajax'])->name('search.ajax');

    // Contact routes
    Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
    Route::post('/contact/send', [ContactController::class, 'send'])->name('contact.send');
    Route::post('/contact/draft', [ContactController::class, 'saveDraft'])->name('contact.draft');

    // Notification routes
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/mark-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-read');
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::get('/notifications/counts', [NotificationController::class, 'getCounts'])->name('notifications.counts');
    Route::post('/notifications/create-alert', [NotificationController::class, 'createAlert'])->name('notifications.create-alert');
    Route::post('/notifications/settings', [NotificationController::class, 'updateSettings'])->name('notifications.settings');

    // Lock Screen routes
    Route::get('/lock-screen', [LockScreenController::class, 'show'])->name('lock-screen.show');
    Route::post('/lock-screen/unlock', [LockScreenController::class, 'unlock'])->name('lock-screen.unlock');
    Route::post('/lock-screen/lock', [LockScreenController::class, 'lock'])->name('lock-screen.lock');
    Route::get('/lock-screen/status', [LockScreenController::class, 'status'])->name('lock-screen.status');
    Route::post('/lock-screen/activity', [LockScreenController::class, 'updateActivity'])->name('lock-screen.activity');


});

// Patient Management Routes - Administrative management system
Route::prefix('patient-management')->name('patient-management.')->middleware('auth')->group(function () {
    // Management Dashboard
    Route::get('/', [PatientManagementController::class, 'dashboard'])->name('dashboard');

    // Patient Registry & Profiles
    Route::prefix('registry')->name('registry.')->group(function () {
        Route::get('/', [PatientManagementController::class, 'registry'])->name('index');
        Route::get('/search', [PatientManagementController::class, 'search'])->name('search');
        Route::get('/active', [PatientManagementController::class, 'activePatients'])->name('active');
        Route::get('/new-registrations', [PatientManagementController::class, 'newRegistrations'])->name('new');
        Route::get('/demographics', [PatientManagementController::class, 'demographics'])->name('demographics');
        Route::get('/create', [PatientManagementController::class, 'createPatient'])->name('create');
        Route::post('/store', [PatientManagementController::class, 'storePatient'])->name('store');
        Route::get('/edit/{id}', [PatientManagementController::class, 'editPatient'])->name('edit');
        Route::put('/update/{id}', [PatientManagementController::class, 'updatePatient'])->name('update');
        Route::get('/view/{id}', [PatientManagementController::class, 'viewPatient'])->name('view');
        Route::get('/medical-history/{id}', [PatientManagementController::class, 'medicalHistory'])->name('medical-history');
        Route::get('/assigned', [PatientManagementController::class, 'assignedPatients'])->name('assigned');
        Route::get('/my-assigned', [PatientManagementController::class, 'myAssignedPatients'])
            ->name('my-assigned')
            ->middleware(\App\Http\Middleware\CheckPermission::class . ':patients.view_assigned');
    });

    // Patient Notes Routes
    Route::prefix('notes')->name('notes.')->group(function () {
        Route::post('/store', [PatientNoteController::class, 'store'])->name('store');
        Route::get('/patient/{patientId}', [PatientNoteController::class, 'getPatientNotes'])->name('patient');
        Route::get('/{note}', [PatientNoteController::class, 'show'])->name('show');
        Route::put('/{note}', [PatientNoteController::class, 'update'])->name('update');
        Route::delete('/{note}', [PatientNoteController::class, 'destroy'])->name('destroy');
    });
});

// Therapist Management Routes - Administrative management system (No authentication required)
Route::prefix('therapist-management')->name('therapist-management.')->group(function () {
    // Management Dashboard
    Route::get('/', [TherapistManagementController::class, 'dashboard'])->name('dashboard');

    // Therapist Directory & Profiles
    Route::prefix('directory')->name('directory.')->group(function () {
        Route::get('/', [TherapistManagementController::class, 'directory'])->name('index');
        Route::get('/active', [TherapistManagementController::class, 'activeTherapists'])->name('active');
        Route::get('/specializations', [TherapistManagementController::class, 'specializations'])->name('specializations');
        Route::get('/create', [TherapistManagementController::class, 'createTherapist'])->name('create');
        Route::post('/store', [TherapistManagementController::class, 'storeTherapist'])->name('store');
        Route::get('/edit/{id}', [TherapistManagementController::class, 'editTherapist'])->name('edit');
        Route::put('/update/{id}', [TherapistManagementController::class, 'updateTherapist'])->name('update');
        Route::get('/view/{id}', [TherapistManagementController::class, 'viewTherapist'])->name('view');
        Route::get('/constellation/{id}', [TherapistManagementController::class, 'patientConstellation'])->name('constellation');
    });

    // Professional Development
    Route::prefix('development')->name('development.')->group(function () {
        Route::get('/', [TherapistManagementController::class, 'professionalDevelopment'])->name('index');
        Route::get('/training', [TherapistManagementController::class, 'training'])->name('training');
        Route::get('/certifications', [TherapistManagementController::class, 'certifications'])->name('certifications');
    });

    // Consultation System
    Route::prefix('consultations')->name('consultations.')->group(function () {
        Route::get('/', [TherapistManagementController::class, 'consultations'])->name('index');
        Route::get('/schedule', [TherapistManagementController::class, 'scheduleConsultation'])->name('schedule');
        Route::post('/store', [TherapistManagementController::class, 'storeConsultation'])->name('store');
    });

    // Treatment Analytics
    Route::prefix('analytics')->name('analytics.')->group(function () {
        Route::get('/', [TherapistManagementController::class, 'analytics'])->name('index');
        Route::get('/performance', [TherapistManagementController::class, 'performanceAnalytics'])->name('performance');
        Route::get('/outcomes', [TherapistManagementController::class, 'outcomeAnalytics'])->name('outcomes');
    });

    // Referral Management
    Route::prefix('referrals')->name('referrals.')->group(function () {
        Route::get('/', [TherapistManagementController::class, 'referrals'])->name('index');
        Route::get('/create', [TherapistManagementController::class, 'createReferral'])->name('create');
        Route::post('/store', [TherapistManagementController::class, 'storeReferral'])->name('store');
    });

    // Clinic Assignment
    Route::prefix('clinics')->name('clinics.')->group(function () {
        Route::get('/assign', [TherapistManagementController::class, 'assignClinic'])->name('assign');
        Route::post('/assign', [TherapistManagementController::class, 'storeClinicAssignment'])->name('store-assignment');
    });
});

// Clinic Management Routes - Administrative management system (No authentication required)
Route::prefix('clinic-management')->name('clinic-management.')->group(function () {
    // Management Dashboard
    Route::get('/', [ClinicManagementController::class, 'dashboard'])->name('dashboard');

    // Clinic Registry & Profiles
    Route::prefix('registry')->name('registry.')->group(function () {
        Route::get('/', [ClinicManagementController::class, 'registry'])->name('index');
        Route::get('/active', [ClinicManagementController::class, 'activeClinics'])->name('active');
        Route::get('/new-registrations', [ClinicManagementController::class, 'newRegistrations'])->name('new');
        Route::get('/locations', [ClinicManagementController::class, 'locations'])->name('locations');
        Route::get('/create', [ClinicManagementController::class, 'createClinic'])->name('create');
        Route::post('/store', [ClinicManagementController::class, 'storeClinic'])->name('store');
        Route::get('/edit/{id}', [ClinicManagementController::class, 'editClinic'])->name('edit');
        Route::put('/update/{id}', [ClinicManagementController::class, 'updateClinic'])->name('update');
        Route::get('/view/{id}', [ClinicManagementController::class, 'viewClinic'])->name('view');
        Route::get('/operational-history/{id}', [ClinicManagementController::class, 'operationalHistory'])->name('operational-history');
    });

    // Staff Operations
    Route::prefix('staff')->name('staff.')->group(function () {
        Route::get('/', [ClinicManagementController::class, 'staffOperations'])->name('index');
        Route::get('/directory', [ClinicManagementController::class, 'staffDirectory'])->name('directory');
        Route::get('/scheduling', [ClinicManagementController::class, 'staffScheduling'])->name('scheduling');
        Route::get('/performance', [ClinicManagementController::class, 'staffPerformance'])->name('performance');
        Route::get('/training', [ClinicManagementController::class, 'staffTraining'])->name('training');
        Route::get('/recruitment', [ClinicManagementController::class, 'staffRecruitment'])->name('recruitment');
    });

    // Service Delivery
    Route::prefix('services')->name('services.')->group(function () {
        Route::get('/', [ClinicManagementController::class, 'serviceDelivery'])->name('index');
        Route::get('/catalog', [ClinicManagementController::class, 'serviceCatalog'])->name('catalog');
        Route::get('/quality', [ClinicManagementController::class, 'qualityAssurance'])->name('quality');
        Route::get('/patient-flow', [ClinicManagementController::class, 'patientFlow'])->name('patient-flow');
        Route::get('/metrics', [ClinicManagementController::class, 'serviceMetrics'])->name('metrics');
        Route::get('/improvement', [ClinicManagementController::class, 'serviceImprovement'])->name('improvement');
    });

    // Performance Analytics
    Route::prefix('analytics')->name('analytics.')->group(function () {
        Route::get('/', [ClinicManagementController::class, 'performanceAnalytics'])->name('index');
        Route::get('/kpi', [ClinicManagementController::class, 'kpiDashboard'])->name('kpi');
        Route::get('/utilization', [ClinicManagementController::class, 'utilizationAnalytics'])->name('utilization');
        Route::get('/satisfaction', [ClinicManagementController::class, 'satisfactionAnalytics'])->name('satisfaction');
        Route::get('/financial', [ClinicManagementController::class, 'financialAnalytics'])->name('financial');
        Route::get('/operational', [ClinicManagementController::class, 'operationalAnalytics'])->name('operational');
    });
});

// Researcher Management Routes - Administrative management system (Authentication required)
Route::prefix('researcher-management')->name('researcher-management.')->middleware(['auth'])->group(function () {
    // Management Dashboard
    Route::get('/', [ResearcherManagementController::class, 'dashboard'])->name('dashboard');

    // Researcher Registry & Profiles
    Route::prefix('registry')->name('registry.')->group(function () {
        Route::get('/', [ResearcherManagementController::class, 'registry'])->name('index');
        Route::get('/active', [ResearcherManagementController::class, 'activeResearchers'])->name('active');
        Route::get('/new-registrations', [ResearcherManagementController::class, 'newRegistrations'])->name('new');
        Route::get('/specializations', [ResearcherManagementController::class, 'specializations'])->name('specializations');
        Route::get('/institutions', [ResearcherManagementController::class, 'institutions'])->name('institutions');
        Route::get('/create', [ResearcherManagementController::class, 'createResearcher'])->name('create');
        Route::post('/store', [ResearcherManagementController::class, 'storeResearcher'])->name('store');
        Route::get('/edit/{id}', [ResearcherManagementController::class, 'editResearcher'])->name('edit');
        Route::put('/update/{id}', [ResearcherManagementController::class, 'updateResearcher'])->name('update');
        Route::get('/view/{id}', [ResearcherManagementController::class, 'viewResearcher'])->name('view');
        Route::get('/credentials/{id}', [ResearcherManagementController::class, 'researcherCredentials'])->name('credentials');
    });

    // Analytics
    Route::prefix('analytics')->name('analytics.')->group(function () {
        Route::get('/', [ResearcherManagementController::class, 'analyticsIndex'])->name('index');
        Route::get('/performance', [ResearcherManagementController::class, 'performanceMetrics'])->name('performance');
        Route::get('/impact', [ResearcherManagementController::class, 'impactAnalysis'])->name('impact');
        Route::get('/productivity', [ResearcherManagementController::class, 'productivityTrends'])->name('productivity');
        Route::get('/benchmarking', [ResearcherManagementController::class, 'researchBenchmarking'])->name('benchmarking');
        Route::post('/export', [ResearcherManagementController::class, 'exportAnalytics'])->name('export');
        Route::get('/download/{format}/{timestamp}', [ResearcherManagementController::class, 'downloadAnalytics'])->name('download');
    });
});

// User Management Routes - Administrative management system (Authentication required)
Route::prefix('user-management')->name('user-management.')->middleware(['auth'])->group(function () {
    // User Registry & Management
    Route::get('/', [UserManagementController::class, 'index'])->name('index');
    Route::get('/create', [UserManagementController::class, 'create'])->name('create');
    Route::post('/store', [UserManagementController::class, 'store'])->name('store');
    Route::get('/edit/{user}', [UserManagementController::class, 'edit'])->name('edit');
    Route::put('/update/{user}', [UserManagementController::class, 'update'])->name('update');
    Route::get('/show/{user}', [UserManagementController::class, 'show'])->name('show');
    Route::delete('/destroy/{user}', [UserManagementController::class, 'destroy'])->name('destroy');

    // AJAX endpoints
    Route::get('/users-by-role', [UserManagementController::class, 'getUsersByRole'])->name('users-by-role');
    Route::get('/users-by-clinic', [UserManagementController::class, 'getUsersByClinic'])->name('users-by-clinic');
});

// Role Management Routes - Administrative management system (Authentication required)
Route::prefix('role-management')->name('role-management.')->middleware(['auth'])->group(function () {
    // Role CRUD operations
    Route::get('/', [RoleManagementController::class, 'index'])->name('index');
    Route::get('/create', [RoleManagementController::class, 'create'])->name('create');
    Route::post('/store', [RoleManagementController::class, 'store'])->name('store');
    Route::get('/show/{role}', [RoleManagementController::class, 'show'])->name('show');
    Route::get('/edit/{role}', [RoleManagementController::class, 'edit'])->name('edit');
    Route::put('/update/{role}', [RoleManagementController::class, 'update'])->name('update');
    Route::delete('/destroy/{role}', [RoleManagementController::class, 'destroy'])->name('destroy');

    // Permission management for roles
    Route::get('/permissions/{role}', [RoleManagementController::class, 'permissions'])->name('permissions');
    Route::put('/permissions/{role}', [RoleManagementController::class, 'updatePermissions'])->name('update-permissions');

    // User role assignment
    Route::post('/assign-user/{role}', [RoleManagementController::class, 'assignToUser'])->name('assign-user');
    Route::post('/remove-user/{role}', [RoleManagementController::class, 'removeFromUser'])->name('remove-user');

    // Demo page
    Route::get('/demo', function () {
        return view('admin.role-management.demo');
    })->name('demo');
});

// Permission Management Routes - Administrative management system (Authentication required)
Route::prefix('permission-management')->name('permission-management.')->middleware(['auth'])->group(function () {
    // Permission CRUD operations
    Route::get('/', [PermissionManagementController::class, 'index'])->name('index');
    Route::get('/create', [PermissionManagementController::class, 'create'])->name('create');
    Route::post('/store', [PermissionManagementController::class, 'store'])->name('store');
    Route::get('/show/{permission}', [PermissionManagementController::class, 'show'])->name('show');
    Route::get('/edit/{permission}', [PermissionManagementController::class, 'edit'])->name('edit');
    Route::put('/update/{permission}', [PermissionManagementController::class, 'update'])->name('update');
    Route::delete('/destroy/{permission}', [PermissionManagementController::class, 'destroy'])->name('destroy');

    // Grouped permissions view
    Route::get('/grouped', [PermissionManagementController::class, 'grouped'])->name('grouped');

    // Bulk operations
    Route::post('/bulk-assign', [PermissionManagementController::class, 'bulkAssign'])->name('bulk-assign');
    Route::post('/bulk-remove', [PermissionManagementController::class, 'bulkRemove'])->name('bulk-remove');

    // Permission groups
    Route::get('/groups', [PermissionManagementController::class, 'groups'])->name('groups');
});

// User Role Assignment Routes - For managing user role assignments (Authentication required)
Route::prefix('user-role-assignment')->name('user-role-assignment.')->middleware(['auth'])->group(function () {
    Route::get('/', [UserRoleAssignmentController::class, 'index'])->name('index');
    Route::get('/bulk', [UserRoleAssignmentController::class, 'bulk'])->name('bulk');
    Route::get('/matrix', [UserRoleAssignmentController::class, 'matrix'])->name('matrix');
    Route::post('/assign', [UserRoleAssignmentController::class, 'assign'])->name('assign');
    Route::post('/revoke', [UserRoleAssignmentController::class, 'revoke'])->name('revoke');
    Route::post('/bulk-assign', [UserRoleAssignmentController::class, 'bulkAssign'])->name('bulk-assign');
});

// Diagnoses Management Routes - Administrative management system with permission protection
Route::prefix('diagnoses-management')->name('diagnoses-management.')->group(function () {
    // Main diagnoses CRUD operations
    Route::get('/', [DiagnosesManagementController::class, 'index'])->name('index')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.view');
    Route::get('/create', [DiagnosesManagementController::class, 'create'])->name('create')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.create');
    Route::post('/store', [DiagnosesManagementController::class, 'store'])->name('store')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.create');
    Route::get('/show/{id}', [DiagnosesManagementController::class, 'show'])->name('show')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.view');
    Route::get('/edit/{id}', [DiagnosesManagementController::class, 'edit'])->name('edit')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.edit');
    Route::put('/update/{id}', [DiagnosesManagementController::class, 'update'])->name('update')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.edit');
    Route::delete('/destroy/{id}', [DiagnosesManagementController::class, 'destroy'])->name('destroy')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.delete');

    // Bulk upload and export
    Route::get('/upload', [DiagnosesManagementController::class, 'showUpload'])->name('upload')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.create');
    Route::post('/bulk-upload', [DiagnosesManagementController::class, 'bulkUpload'])->name('bulk-upload')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.create');
    Route::get('/export', [DiagnosesManagementController::class, 'export'])->name('export')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.view');

    // Diagnosis categories management
    Route::prefix('categories')->name('categories.')->group(function () {
        Route::get('/', [DiagnosesManagementController::class, 'categoriesIndex'])->name('index')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.categories');
        Route::post('/store', [DiagnosesManagementController::class, 'storeCategory'])->name('store')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.categories');
        Route::get('/edit/{id}', [DiagnosesManagementController::class, 'editCategory'])->name('edit')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.categories');
        Route::put('/update/{id}', [DiagnosesManagementController::class, 'updateCategory'])->name('update')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.categories');
        Route::delete('/destroy/{id}', [DiagnosesManagementController::class, 'destroyCategory'])->name('destroy')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.categories');
    });

    // Diagnosis analytics
    Route::prefix('analytics')->name('analytics.')->group(function () {
        Route::get('/', [DiagnosesManagementController::class, 'analyticsIndex'])->name('index')->middleware(\App\Http\Middleware\CheckPermission::class . ':diagnoses.analytics');
        // Additional analytics routes can be added here
    });
});

// System Logs Routes - Administrative management system (Authentication required)
Route::prefix('system-logs')->name('system-logs.')->middleware(['auth'])->group(function () {
    // Log viewing operations
    Route::get('/', [SystemLogsController::class, 'index'])->name('index');
    Route::get('/show/{systemLog}', [SystemLogsController::class, 'show'])->name('show');

    // Export functionality
    Route::get('/export', [SystemLogsController::class, 'export'])->name('export');

    // Cleanup operations
    Route::post('/cleanup', [SystemLogsController::class, 'cleanup'])->name('cleanup');

    // API endpoints for widgets and AJAX
    Route::get('/recent-activity', [SystemLogsController::class, 'recentActivity'])->name('recent-activity');
    Route::get('/for-model/{modelType}/{modelId}', [SystemLogsController::class, 'forModel'])->name('for-model');
});

require __DIR__.'/auth.php';
