<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Marbar-Africa | Dashboard</title>
    <!--begin::Primary Meta Tags-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="title" content="Marbar-Africa-Dashboard"/>
    <meta name="author" content="Peter_Njuguna_Mungai"/>
    <meta name="description"
        content="Marbar-Africa is a Data Analytical system that provides insights into the performance of the Marbar-Africa platform, enabling users to make informed decisions based on data-driven analysis." />
    <meta name="keywords"
        content="marbar-africa, data analytics, performance insights, data-driven decisions, marbar africa dashboard, marbar africa platform" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <!--end::Primary Meta Tags-->
    <!--begin::Fonts-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css"
        integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous" />
    <!--end::Fonts-->
    <!--begin::Third Party Plugin(OverlayScrollbars)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.10.1/styles/overlayscrollbars.min.css"
        integrity="sha256-tZHrRjVqNSRyWg2wbppGnT833E/Ys0DHWGwT04GiqQg=" crossorigin="anonymous" />
    <!--end::Third Party Plugin(OverlayScrollbars)-->
    <!--begin::Third Party Plugin(Bootstrap Icons)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
        integrity="sha256-9kPW/n5nn53j4WMRYAxe9c1rCY96Oogo/MKSVdKzPmI=" crossorigin="anonymous" />

    <link rel="stylesheet" href="../../../dist/css/adminlte.css" />
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
    <script type="text/javascript"
                    src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>




    <!--end::Required Plugin(AdminLTE)-->
    <!-- Custom Styling for Header and Sidebar -->
    <style>
        /* Navigation Header - Blue Background */
        .app-header.navbar {
            background-color: #007bff !important;
            border-bottom: 1px solid #0056b3;
        }

        .app-header.navbar .navbar-nav .nav-link {
            color: #ffffff !important;
        }

        .app-header.navbar .navbar-nav .nav-link:hover {
            color: #e3f2fd !important;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
        }

        .app-header.navbar .navbar-nav .nav-link:focus {
            color: #e3f2fd !important;
        }

        /* Search input styling in blue header */
        .app-header .form-control {
            border-color: rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

        .app-header .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .app-header .form-control:focus {
            border-color: #ffffff;
            background-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }

        .app-header .btn-outline-secondary {
            border-color: rgba(255, 255, 255, 0.3);
            color: #ffffff;
        }

        .app-header .btn-outline-secondary:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: #ffffff;
        }

        /* Dropdown badges in header */
        .app-header .navbar-badge {
            background-color: #dc3545 !important;
            color: #ffffff;
        }

        /* User dropdown styling */
        .app-header .user-image {
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Sidebar - White Background (Light Theme) */
        .app-sidebar {
            background-color: #ffffff !important;
            border-right: 1px solid #dee2e6;
        }

        /* Override sidebar background for light theme specifically */
        [data-theme="light"] .app-sidebar,
        :root .app-sidebar {
            background-color: #ffffff !important;
            border-right: 1px solid #dee2e6;
        }

        /* Sidebar brand area - Blue Background */
        .app-sidebar .sidebar-brand {
            background-color: #007bff !important;
            border-bottom: 1px solid #0056b3;
        }

        .app-sidebar .sidebar-brand .brand-link {
            color: #ffffff !important;
        }

        .app-sidebar .sidebar-brand .brand-text {
            color: #ffffff !important;
        }

        /* Logo image styling in blue brand area */
        .app-sidebar .sidebar-brand .brand-image {
            filter: brightness(0) invert(1);
            opacity: 0.9;
        }

        /* Sidebar navigation */
        .app-sidebar .sidebar-menu .nav-link {
            color: #495057 !important;
        }

        .app-sidebar .sidebar-menu .nav-link:hover {
            background-color: #f8f9fa !important;
            color: #007bff !important;
        }

        .app-sidebar .sidebar-menu .nav-link.active {
            background-color: #007bff !important;
            color: #ffffff !important;
        }

        .app-sidebar .sidebar-menu .nav-link.active .nav-icon {
            color: #ffffff !important;
        }

        /* Sidebar headers */
        .app-sidebar .nav-header {
            color: #6c757d !important;
            background-color: transparent;
        }

        /* Sidebar treeview */
        .app-sidebar .nav-treeview .nav-link {
            color: #6c757d !important;
            padding-left: 2.5rem;
        }

        .app-sidebar .nav-treeview .nav-link:hover {
            background-color: #f8f9fa !important;
            color: #007bff !important;
        }

        .app-sidebar .nav-treeview .nav-link.active {
            background-color: #e3f2fd !important;
            color: #007bff !important;
        }

        /* Sidebar badges */
        .app-sidebar .nav-badge {
            background-color: #007bff !important;
            color: #ffffff;
        }

        /* Sidebar icons */
        .app-sidebar .nav-icon {
            color: #6c757d;
        }

        .app-sidebar .nav-link:hover .nav-icon {
            color: #007bff !important;
        }

        .app-sidebar .nav-link.active .nav-icon {
            color: #ffffff !important;
        }

        /* Remove dark theme from sidebar */
        .app-sidebar[data-bs-theme="dark"] {
            --bs-body-bg: #ffffff;
            --bs-body-color: #495057;
        }

        /* Dark Theme Header Adjustments */
        [data-theme="dark"] .app-header.navbar {
            background-color: #1a1a1a !important;
            border-bottom: 1px solid #404040 !important;
        }

        [data-theme="dark"] .app-header.navbar .navbar-nav .nav-link {
            color: #ffffff !important;
        }

        [data-theme="dark"] .app-header.navbar .navbar-nav .nav-link:hover {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.1) !important;
        }

        /* Dark Theme Search Input */
        [data-theme="dark"] .app-header .form-control {
            background-color: #3a3a3a !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .app-header .form-control::placeholder {
            color: #b0b0b0 !important;
        }

        [data-theme="dark"] .app-header .form-control:focus {
            background-color: #3a3a3a !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        [data-theme="dark"] .app-header .btn-outline-secondary {
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .app-header .btn-outline-secondary:hover {
            background-color: #007bff !important;
            border-color: #007bff !important;
        }

        /* Dark Theme Dropdown Menus in Header */
        [data-theme="dark"] .app-header .dropdown-menu {
            background-color: #2d2d2d !important;
            border-color: #404040 !important;
        }

        [data-theme="dark"] .app-header .dropdown-item {
            color: #ffffff !important;
        }

        [data-theme="dark"] .app-header .dropdown-item:hover {
            background-color: #3a3a3a !important;
            color: #007bff !important;
        }

        /* Theme Toggle Button Specific Styles */
        .theme-toggle {
            transition: all 0.3s ease !important;
        }

        .theme-toggle:hover {
            transform: scale(1.1) !important;
        }

        /* Dark theme user dropdown adjustments */
        [data-theme="dark"] .user-header {
            background-color: #007bff !important;
        }

        [data-theme="dark"] .user-body {
            background-color: #2d2d2d !important;
        }

        [data-theme="dark"] .user-body a {
            color: #ffffff !important;
        }

        [data-theme="dark"] .user-body a:hover {
            color: #007bff !important;
        }

        [data-theme="dark"] .user-footer {
            background-color: #2d2d2d !important;
            border-top: 1px solid #404040 !important;
        }

        [data-theme="dark"] .user-footer .btn {
            background-color: #007bff !important;
            border-color: #007bff !important;
            color: #ffffff !important;
        }
    </style>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css">
</head>
<?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/includes/header.blade.php ENDPATH**/ ?>