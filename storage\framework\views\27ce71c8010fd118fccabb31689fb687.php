<!doctype html>
<html lang="en" id="html-root">
<!--begin::Head-->
<?php echo $__env->make('includes.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<body class="layout-fixed sidebar-expand-lg bg-body-tertiary" id="app-body">
    <!--begin::App Wrapper-->
    <div class="app-wrapper">
        <!--begin::Header-->
        <?php echo $__env->make('includes.nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!--end::Header-->
        <!--begin::Sidebar-->
        <?php echo $__env->make('includes.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!--end::Sidebar-->
        <!--begin::App Main-->
        <?php echo $__env->yieldContent('content'); ?>
        <!--end::App Main-->
        <!--begin::Footer-->
        <footer class="app-footer">
            <!--begin::To the end-->
            <div class="float-end d-none d-sm-inline">Anything you want</div>
            <!--end::To the end-->
            <!--begin::Copyright-->
            <strong>
                Copyright &copy; 2025-2026&nbsp;
                <a href="https://marbar-africa.grandtek.co.ke/" class="text-decoration-none">Marbar-Africa</a>.
            </strong>
            All rights reserved.
            <!--end::Copyright-->
        </footer>
        <!--end::Footer-->
    </div>
    <!--end::App Wrapper-->
    <!--begin::Script-->
    <!--begin::Third Party Plugin(OverlayScrollbars)-->
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.10.1/browser/overlayscrollbars.browser.es6.min.js"
        integrity="sha256-dghWARbRe2eLlIJ56wNB+b760ywulqK3DzZYEpsg2fQ=" crossorigin="anonymous"></script>
    <!--end::Third Party Plugin(OverlayScrollbars)-->
    <!--begin::Required Plugin(popperjs for Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous">
    </script>
    <!--end::Required Plugin(popperjs for Bootstrap 5)-->
    <!--begin::Required Plugin(Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
        integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy" crossorigin="anonymous">
    </script>
    <!--end::Required Plugin(Bootstrap 5)-->
    <!--begin::Required Plugin(AdminLTE)-->
    <script src="../../dist/js/adminlte.js"></script>
    <!--end::Required Plugin(AdminLTE)-->
    <!--begin::OverlayScrollbars Configure-->
    <script>
        const SELECTOR_SIDEBAR_WRAPPER = '.sidebar-wrapper';
      const Default = {
        scrollbarTheme: 'os-theme-light',
        scrollbarAutoHide: 'leave',
        scrollbarClickScroll: true,
      };
      document.addEventListener('DOMContentLoaded', function () {
        const sidebarWrapper = document.querySelector(SELECTOR_SIDEBAR_WRAPPER);
        if (sidebarWrapper && typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== 'undefined') {
          OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
            scrollbars: {
              theme: Default.scrollbarTheme,
              autoHide: Default.scrollbarAutoHide,
              clickScroll: Default.scrollbarClickScroll,
            },
          });
        }
      });
    </script>
    <!--end::OverlayScrollbars Configure-->
    <!-- OPTIONAL SCRIPTS -->
    <!-- apexcharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.37.1/dist/apexcharts.min.js"
        integrity="sha256-+vh8GkaU7C9/wbSLIcwq82tQ2wTf44aOHA8HlBMwRI8=" crossorigin="anonymous"></script>
    <script>
        // NOTICE!! DO NOT USE ANY OF THIS JAVASCRIPT
      // IT'S ALL JUST JUNK FOR DEMO
      // ++++++++++++++++++++++++++++++++++++++++++

      /* apexcharts
       * -------
       * Here we will create a few charts using apexcharts
       */

      //-----------------------
      // - MONTHLY SALES CHART -
      //-----------------------

      const sales_chart_options = {
        series: [
          {
            name: 'Digital Goods',
            data: [28, 48, 40, 19, 86, 27, 90],
          },
          {
            name: 'Electronics',
            data: [65, 59, 80, 81, 56, 55, 40],
          },
        ],
        chart: {
          height: 180,
          type: 'area',
          toolbar: {
            show: false,
          },
        },
        legend: {
          show: false,
        },
        colors: ['#0d6efd', '#20c997'],
        dataLabels: {
          enabled: false,
        },
        stroke: {
          curve: 'smooth',
        },
        xaxis: {
          type: 'datetime',
          categories: [
            '2023-01-01',
            '2023-02-01',
            '2023-03-01',
            '2023-04-01',
            '2023-05-01',
            '2023-06-01',
            '2023-07-01',
          ],
        },
        tooltip: {
          x: {
            format: 'MMMM yyyy',
          },
        },
      };

      // Only run chart code if we're on a page that has charts
      const salesChartElement = document.querySelector('#sales-chart');
      if (salesChartElement) {
        const sales_chart = new ApexCharts(salesChartElement, sales_chart_options);
        sales_chart.render();
      }

      //---------------------------
      // - END MONTHLY SALES CHART -
      //---------------------------

      function createSparklineChart(selector, data) {
        const options = {
          series: [{ data }],
          chart: {
            type: 'line',
            width: 150,
            height: 30,
            sparkline: {
              enabled: true,
            },
          },
          colors: ['var(--bs-primary)'],
          stroke: {
            width: 2,
          },
          tooltip: {
            fixed: {
              enabled: false,
            },
            x: {
              show: false,
            },
            y: {
              title: {
                formatter: function (seriesName) {
                  return '';
                },
              },
            },
            marker: {
              show: false,
            },
          },
        };

        const chartElement = document.querySelector(selector);
        if (chartElement) {
          const chart = new ApexCharts(chartElement, options);
          chart.render();
        }
      }

      // Only create sparkline charts if we're on the dashboard page
      if (document.querySelector('#table-sparkline-1')) {
        const table_sparkline_1_data = [25, 66, 41, 89, 63, 25, 44, 12, 36, 9, 54];
        const table_sparkline_2_data = [12, 56, 21, 39, 73, 45, 64, 52, 36, 59, 44];
        const table_sparkline_3_data = [15, 46, 21, 59, 33, 15, 34, 42, 56, 19, 64];
        const table_sparkline_4_data = [30, 56, 31, 69, 43, 35, 24, 32, 46, 29, 64];
        const table_sparkline_5_data = [20, 76, 51, 79, 53, 35, 54, 22, 36, 49, 64];
        const table_sparkline_6_data = [5, 36, 11, 69, 23, 15, 14, 42, 26, 19, 44];
        const table_sparkline_7_data = [12, 56, 21, 39, 73, 45, 64, 52, 36, 59, 74];

        createSparklineChart('#table-sparkline-1', table_sparkline_1_data);
        createSparklineChart('#table-sparkline-2', table_sparkline_2_data);
        createSparklineChart('#table-sparkline-3', table_sparkline_3_data);
        createSparklineChart('#table-sparkline-4', table_sparkline_4_data);
        createSparklineChart('#table-sparkline-5', table_sparkline_5_data);
        createSparklineChart('#table-sparkline-6', table_sparkline_6_data);
        createSparklineChart('#table-sparkline-7', table_sparkline_7_data);
      }

      //-------------
      // - PIE CHART -
      //-------------

      const pie_chart_options = {
        series: [700, 500, 400, 600, 300, 100],
        chart: {
          type: 'donut',
        },
        labels: ['Chrome', 'Edge', 'FireFox', 'Safari', 'Opera', 'IE'],
        dataLabels: {
          enabled: false,
        },
        colors: ['#0d6efd', '#20c997', '#ffc107', '#d63384', '#6f42c1', '#adb5bd'],
      };

      const pieChartElement = document.querySelector('#pie-chart');
      if (pieChartElement) {
        const pie_chart = new ApexCharts(pieChartElement, pie_chart_options);
        pie_chart.render();
      }

      //-----------------
      // - END PIE CHART -
      //-----------------
    </script>
    <!--end::Script-->

    <?php echo $__env->yieldPushContent('styles'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Dark Theme CSS and Toggle Functionality -->
    <style>
        /* CSS Variables for Theme System */
        :root {
            /* Light Theme Variables (Default) */
            --theme-bg-primary: #ffffff;
            --theme-bg-secondary: #f8f9fa;
            --theme-bg-tertiary: #e9ecef;
            --theme-text-primary: #212529;
            --theme-text-secondary: #6c757d;
            --theme-text-muted: #adb5bd;
            --theme-border-color: #dee2e6;
            --theme-card-bg: #ffffff;
            --theme-card-border: #dee2e6;
            --theme-input-bg: #ffffff;
            --theme-input-border: #ced4da;
            --theme-table-bg: #ffffff;
            --theme-table-stripe: #f8f9fa;
            --theme-dropdown-bg: #ffffff;
            --theme-dropdown-border: #dee2e6;
            --theme-shadow: rgba(0, 0, 0, 0.15);
            --theme-accent: #007bff;
            --theme-accent-hover: #0056b3;
        }

        /* Dark Theme Variables */
        [data-theme="dark"] {
            --theme-bg-primary: #1a1a1a;
            --theme-bg-secondary: #2d2d2d;
            --theme-bg-tertiary: #3a3a3a;
            --theme-text-primary: #ffffff;
            --theme-text-secondary: #b0b0b0;
            --theme-text-muted: #6c757d;
            --theme-border-color: #404040;
            --theme-card-bg: #2d2d2d;
            --theme-card-border: #404040;
            --theme-input-bg: #3a3a3a;
            --theme-input-border: #555555;
            --theme-table-bg: #2d2d2d;
            --theme-table-stripe: #3a3a3a;
            --theme-dropdown-bg: #2d2d2d;
            --theme-dropdown-border: #404040;
            --theme-shadow: rgba(0, 0, 0, 0.3);
            --theme-accent: #007bff;
            --theme-accent-hover: #0056b3;
        }

        /* Dark Theme Styles */
        [data-theme="dark"] body {
            background-color: var(--theme-bg-primary) !important;
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .bg-body-tertiary {
            background-color: var(--theme-bg-primary) !important;
        }

        /* Dark Theme - Main Content Area */
        [data-theme="dark"] .app-main {
            background-color: var(--theme-bg-primary) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Cards */
        [data-theme="dark"] .card {
            background-color: var(--theme-card-bg) !important;
            border-color: var(--theme-card-border) !important;
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .card-header {
            background-color: var(--theme-bg-secondary) !important;
            border-color: var(--theme-card-border) !important;
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .card-body {
            background-color: var(--theme-card-bg) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Tables */
        [data-theme="dark"] .table {
            background-color: var(--theme-table-bg) !important;
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .table-striped>tbody>tr:nth-of-type(odd)>td,
        [data-theme="dark"] .table-striped>tbody>tr:nth-of-type(odd)>th {
            background-color: var(--theme-table-stripe) !important;
        }

        [data-theme="dark"] .table th,
        [data-theme="dark"] .table td {
            border-color: var(--theme-border-color) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Forms */
        [data-theme="dark"] .form-control {
            background-color: var(--theme-input-bg) !important;
            border-color: var(--theme-input-border) !important;
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .form-control:focus {
            background-color: var(--theme-input-bg) !important;
            border-color: var(--theme-accent) !important;
            color: var(--theme-text-primary) !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        [data-theme="dark"] .form-control::placeholder {
            color: var(--theme-text-muted) !important;
        }

        /* Dark Theme - Dropdowns */
        [data-theme="dark"] .dropdown-menu {
            background-color: var(--theme-dropdown-bg) !important;
            border-color: var(--theme-dropdown-border) !important;
            box-shadow: 0 0.5rem 1rem var(--theme-shadow) !important;
        }

        [data-theme="dark"] .dropdown-item {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .dropdown-item:hover,
        [data-theme="dark"] .dropdown-item:focus {
            background-color: var(--theme-bg-secondary) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Sidebar Adjustments */
        [data-theme="dark"] .app-sidebar {
            background-color: var(--theme-bg-secondary) !important;
            border-right: 1px solid var(--theme-border-color) !important;
        }

        [data-theme="dark"] .app-sidebar .sidebar-menu .nav-link {
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .app-sidebar .sidebar-menu .nav-link:hover {
            background-color: var(--theme-bg-tertiary) !important;
            color: var(--theme-accent) !important;
        }

        [data-theme="dark"] .app-sidebar .nav-header {
            color: var(--theme-text-muted) !important;
        }

        [data-theme="dark"] .app-sidebar .nav-treeview .nav-link {
            color: var(--theme-text-muted) !important;
        }

        [data-theme="dark"] .app-sidebar .nav-treeview .nav-link:hover {
            background-color: var(--theme-bg-tertiary) !important;
            color: var(--theme-accent) !important;
        }

        [data-theme="dark"] .app-sidebar .nav-icon {
            color: var(--theme-text-muted) !important;
        }

        [data-theme="dark"] .app-sidebar .nav-link:hover .nav-icon {
            color: var(--theme-accent) !important;
        }

        /* Dark Theme - Footer */
        [data-theme="dark"] .app-footer {
            background-color: var(--theme-bg-secondary) !important;
            border-top: 1px solid var(--theme-border-color) !important;
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .app-footer a {
            color: var(--theme-accent) !important;
        }

        /* Theme Toggle Button Styles */
        .theme-toggle {
            background: none !important;
            border: none !important;
            color: inherit !important;
            padding: 0.5rem !important;
            border-radius: 0.375rem !important;
            transition: background-color 0.3s ease !important;
        }

        .theme-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }

        .theme-toggle i {
            font-size: 1.1rem !important;
        }

        /* Dark theme specific adjustments for existing components */
        [data-theme="dark"] .text-muted {
            color: var(--theme-text-muted) !important;
        }

        [data-theme="dark"] .border {
            border-color: var(--theme-border-color) !important;
        }

        [data-theme="dark"] .bg-light {
            background-color: var(--theme-bg-secondary) !important;
        }

        [data-theme="dark"] .bg-white {
            background-color: var(--theme-card-bg) !important;
        }

        /* Dark Theme - Info Boxes */
        [data-theme="dark"] .info-box {
            background-color: var(--theme-card-bg) !important;
            color: var(--theme-text-primary) !important;
            border: 1px solid var(--theme-border-color) !important;
        }

        [data-theme="dark"] .info-box-content {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .info-box-text {
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .info-box-number {
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Progress Bars */
        [data-theme="dark"] .progress {
            background-color: var(--theme-bg-tertiary) !important;
        }

        [data-theme="dark"] .progress-text {
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Direct Chat */
        [data-theme="dark"] .direct-chat-messages {
            background-color: var(--theme-bg-secondary) !important;
        }

        [data-theme="dark"] .direct-chat-msg .direct-chat-text {
            background-color: var(--theme-bg-tertiary) !important;
            color: var(--theme-text-primary) !important;
            border-color: var(--theme-border-color) !important;
        }

        [data-theme="dark"] .direct-chat-msg.end .direct-chat-text {
            background-color: var(--theme-accent) !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .direct-chat-name {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .direct-chat-timestamp {
            color: var(--theme-text-muted) !important;
        }

        [data-theme="dark"] .contacts-list {
            background-color: var(--theme-bg-secondary) !important;
        }

        [data-theme="dark"] .contacts-list>li {
            border-bottom: 1px solid var(--theme-border-color) !important;
        }

        [data-theme="dark"] .contacts-list-name {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .contacts-list-msg {
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .contacts-list-date {
            color: var(--theme-text-muted) !important;
        }

        /* Dark Theme - Breadcrumbs */
        [data-theme="dark"] .breadcrumb {
            background-color: transparent !important;
        }

        [data-theme="dark"] .breadcrumb-item {
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .breadcrumb-item.active {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .breadcrumb-item a {
            color: var(--theme-accent) !important;
        }

        /* Dark Theme - Badges */
        [data-theme="dark"] .badge {
            border: 1px solid var(--theme-border-color) !important;
        }

        /* Dark Theme - Buttons */
        [data-theme="dark"] .btn-tool {
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .btn-tool:hover {
            color: var(--theme-text-primary) !important;
            background-color: var(--theme-bg-tertiary) !important;
        }

        /* Dark Theme - App Content Header */
        [data-theme="dark"] .app-content-header {
            background-color: var(--theme-bg-primary) !important;
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .app-content {
            background-color: var(--theme-bg-primary) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - Gradient Cards */
        [data-theme="dark"] .bg-gradient-primary {
            background: linear-gradient(135deg, var(--theme-accent) 0%, #0056b3 100%) !important;
        }

        /* Dark Theme - Text Colors */
        [data-theme="dark"] .text-center {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] h1,
        [data-theme="dark"] h2,
        [data-theme="dark"] h3,
        [data-theme="dark"] h4,
        [data-theme="dark"] h5,
        [data-theme="dark"] h6 {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] p {
            color: var(--theme-text-secondary) !important;
        }

        [data-theme="dark"] .card-title {
            color: var(--theme-text-primary) !important;
        }

        [data-theme="dark"] .card-text {
            color: var(--theme-text-secondary) !important;
        }

        /* Dark Theme - Input Groups */
        [data-theme="dark"] .input-group-text {
            background-color: var(--theme-bg-tertiary) !important;
            border-color: var(--theme-input-border) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Dark Theme - List Groups */
        [data-theme="dark"] .list-group-item {
            background-color: var(--theme-card-bg) !important;
            border-color: var(--theme-border-color) !important;
            color: var(--theme-text-primary) !important;
        }

        /* Smooth transitions for theme switching */
        body,
        .card,
        .app-sidebar,
        .app-footer,
        .form-control,
        .dropdown-menu,
        .info-box,
        .direct-chat-text,
        .breadcrumb-item,
        .btn-tool {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
        }
    </style>

    <!-- Theme Toggle JavaScript -->
    <script>
        // Theme Management System
        class ThemeManager {
            constructor() {
                this.currentTheme = this.getStoredTheme() || 'light';
                this.init();
            }

            init() {
                this.applyTheme(this.currentTheme);
                this.setupToggleButton();
                this.updateScrollbarTheme();
            }

            getStoredTheme() {
                return localStorage.getItem('marbar-theme');
            }

            setStoredTheme(theme) {
                localStorage.setItem('marbar-theme', theme);
            }

            applyTheme(theme) {
                const htmlRoot = document.getElementById('html-root');
                const body = document.getElementById('app-body');

                if (theme === 'dark') {
                    htmlRoot.setAttribute('data-theme', 'dark');
                    body.setAttribute('data-theme', 'dark');
                } else {
                    htmlRoot.removeAttribute('data-theme');
                    body.removeAttribute('data-theme');
                }

                this.currentTheme = theme;
                this.setStoredTheme(theme);
                this.updateToggleIcon();
                this.updateScrollbarTheme();
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                this.applyTheme(newTheme);
            }

            updateToggleIcon() {
                const toggleBtn = document.getElementById('theme-toggle');
                const lightIcon = document.getElementById('theme-light-icon');
                const darkIcon = document.getElementById('theme-dark-icon');

                if (toggleBtn && lightIcon && darkIcon) {
                    if (this.currentTheme === 'dark') {
                        lightIcon.style.display = 'inline';
                        darkIcon.style.display = 'none';
                        toggleBtn.setAttribute('title', 'Switch to Light Theme');
                    } else {
                        lightIcon.style.display = 'none';
                        darkIcon.style.display = 'inline';
                        toggleBtn.setAttribute('title', 'Switch to Dark Theme');
                    }
                }
            }

            updateScrollbarTheme() {
                // Update OverlayScrollbars theme if it exists
                const sidebarWrapper = document.querySelector('.sidebar-wrapper');
                if (sidebarWrapper && typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== 'undefined') {
                    const scrollbarTheme = this.currentTheme === 'dark' ? 'os-theme-dark' : 'os-theme-light';

                    // Reinitialize with new theme
                    OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
                        scrollbars: {
                            theme: scrollbarTheme,
                            autoHide: 'leave',
                            clickScroll: true,
                        },
                    });
                }
            }

            setupToggleButton() {
                const toggleBtn = document.getElementById('theme-toggle');
                if (toggleBtn) {
                    toggleBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.toggleTheme();
                    });
                }
            }
        }

        // Initialize theme manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            window.themeManager = new ThemeManager();
        });
    </script>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css">
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Local IP Detection for System Logs -->
    <script src="<?php echo e(asset('js/local-ip-detector.js')); ?>"></script>

    <!--begin::Lock Screen System-->
    <?php if(auth()->guard()->check()): ?>
    <script src="<?php echo e(asset('js/lock-screen.js')); ?>"></script>
    <?php endif; ?>
    <!--end::Lock Screen System-->

    <script>
        <?php if(Session::has('message')): ?>
    var type = "<?php echo e(Session::get('alert-type','info')); ?>"
    switch(type){
       case 'info':
       toastr.info(" <?php echo e(Session::get('message')); ?> ");
       break;

       case 'success':
       toastr.success(" <?php echo e(Session::get('message')); ?> ");
       break;

       case 'warning':
       toastr.warning(" <?php echo e(Session::get('message')); ?> ");
       break;

       case 'error':
       toastr.error(" <?php echo e(Session::get('message')); ?> ");
       break;
    }
    <?php endif; ?>
    </script>
</body>
<!--end::Body-->

</html>
<?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/main.blade.php ENDPATH**/ ?>